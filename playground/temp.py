import structlog

from metaloader_rest_api.celery_tasks.logic import load_resources_from_providers
from metaloader_rest_api.logging import setup_logging
from metaloader_rest_api.logic import create_tx
from metaloader_rest_api.models import SessionFactory

setup_logging(log_format="pretty")

logger = structlog.stdlib.get_logger(__name__)

get_session = SessionFactory(
    database_url="postgresql://d0_mon_loader_tec:d0_mon_loader_tec@localhost:5432/d0_mon",
)
session = get_session()

load_id, effective_date = create_tx(session, logger)
load_resources_from_providers(
    session=session,
    load_id=load_id,
    effective_date=effective_date,
    module="ETL-SCALE-core",
    version="999.999.999",
    uni_provider_base_url="https://develop-uni.dtpl.corp.dev.vtb/api/1.0/",
    ceh_provider_base_url="https://develop-ceh.dtpl.corp.dev.vtb/api/0.6/",
    limit=10,
)
