from datetime import datetime
from itertools import repeat
from pathlib import Path
from typing import Any, Iterable, List, Optional, Sequence, Tuple
from uuid import UUID

from airflow_client.client.model.dag import DAG
from airflow_client.client.model.tag import Tag
from airflow_client.client.model.task import Task
from sqlalchemy.orm import Session
from structlog.stdlib import get_logger
from typing_extensions import Self

from metaloader_rest_api.af_client import AfClient
from metaloader_rest_api.af_defaults import AfDefaults
from metaloader_rest_api.common_repository import SessionResource
from metaloader_rest_api.flow_model import FlowType
from metaloader_rest_api.flow_repository import (
    FlowRepository,
    StageFlow,
    StageFlowRepository,
    StageMasterFlow,
    StageMasterFlowLink,
    StageMasterFlowLinkRepository,
    StageMasterFlowRepository,
)
from metaloader_rest_api.loggable import Loggable
from metaloader_rest_api.module_repository import ModuleRepository
from metaloader_rest_api.version_repository import VersionRepository

logger = get_logger(__name__)


class FlowAfLoader(Loggable):
    def __init__(
        self,
        flow_repository: FlowRepository,
        stage_flow_repository: StageFlowRepository,
        stage_master_flow_repository: StageMasterFlowRepository,
        stage_master_flow_link_repository: StageMasterFlowLinkRepository,
        version_repository: VersionRepository,
        module_repository: ModuleRepository,
        session_resource: SessionResource,
        af_client: AfClient,
    ):
        self._log = logger.bind(action="load_af_flow")

        self._flow_repository = flow_repository
        self._stage_flow_repository = stage_flow_repository
        self._stage_master_flow_repository = stage_master_flow_repository
        self._stage_master_flow_link_repository = stage_master_flow_link_repository
        self._version_repository = version_repository
        self._module_repository = module_repository
        self._session_resource = session_resource
        self._af_client = af_client

    def with_context(self, **kwarg) -> Self:
        for loggable in [
            self._flow_repository,
            self._stage_flow_repository,
            self._stage_master_flow_repository,
            self._stage_master_flow_link_repository,
            self._version_repository,
            self._module_repository,
            self._session_resource,
            self._af_client,
        ]:
            loggable.with_context(**kwarg)
        return super().with_context(**kwarg)

    def load(
        self,
        module: str,
        version: Tuple[int, int, int],
        effective_date: datetime,
        keep_dag: bool = True,
        with_tasks: bool = True,
        parse_master_flows: bool = True,
        page_size: int = AfDefaults.DAG_PAGE_SIZE.value,
        limit: int = AfDefaults.DAG_LIMIT.value,
    ) -> Any:
        self._log.info(
            "begin",
            keep_dag=keep_dag,
            with_tasks=with_tasks,
            parse_master_flows=parse_master_flows,
            page_size=page_size,
            limit=limit,
        )

        module_id = self._module_repository.get_id(module)
        self._log.info("get_module_id", module_id=module_id)

        with self._session_resource(
            "load_flow_stage"
        ), self._stage_flow_repository as stage_flow_repository, self._stage_master_flow_repository as stage_master_flow_repository, self._stage_master_flow_link_repository as stage_master_flow_link_repository, self._af_client as af_client:
            stage_flow_factory = StageFlowFactory(keep_dag)
            stage_master_flow_factory = StageMasterFlowFactory()
            for dags in af_client.get_dags(page_size, limit):
                dags_tasks = af_client.get_dags_tasks(dags) if with_tasks else None

                stage_flows = stage_flow_factory(dags, dags_tasks)
                stage_flow_repository.load(stage_flows)

                if parse_master_flows:
                    for index, dag in enumerate(dags):
                        if not StageMasterFlowFactory.is_master_flow(dag):
                            continue
                        dag_tasks = (
                            dags_tasks[index]
                            if with_tasks
                            else af_client.get_dag_tasks(dag.dag_id)
                        )
                        stage_master_flow, stage_master_flow_links = (
                            stage_master_flow_factory(dag.dag_id, dag_tasks)
                        )
                        stage_master_flow_repository.load(stage_master_flow)
                        stage_master_flow_link_repository.load(stage_master_flow_links)

        with self._session_resource("load_flow"):
            version_id = self._version_repository.put(
                module_id=module_id,
                major=version[0],
                minor=version[1],
                fix=version[2],
                delivery_date=effective_date,
            )
            self._flow_repository.load(
                module_id,
                version_id,
                effective_date,
                flow_stage_table=self._stage_flow_repository.table,
                master_flow_stage_table=self._stage_master_flow_repository.table,
                master_flow_link_stage_table=self._stage_master_flow_link_repository.table,
            )

        self._log.info("end", version_id=version_id)
        return None


def af_load_flows(
    session: Session,
    load_id: UUID,
    module: str,
    version: str,
    effective_date: datetime,
    af_url: str,
    af_username: str,
    af_password: str,
    page_size: int = AfDefaults.DAG_PAGE_SIZE.value,
    keep_dag: bool = True,
    with_tasks: bool = True,
    parse_master_flows: bool = True,
    limit: int = AfDefaults.DAG_LIMIT.value,
) -> Any:
    stage_params = {
        "session": session,
        "table_id": load_id.hex,
        "page_size": page_size,
    }
    flow_af_loader = FlowAfLoader(
        flow_repository=FlowRepository(session),
        stage_flow_repository=StageFlowRepository(**stage_params),
        stage_master_flow_repository=StageMasterFlowRepository(**stage_params),
        stage_master_flow_link_repository=StageMasterFlowLinkRepository(**stage_params),
        version_repository=VersionRepository(session),
        module_repository=ModuleRepository(session),
        session_resource=SessionResource(session),
        af_client=AfClient(
            url=af_url,
            username=af_username,
            password=af_password,
        ),
    ).with_context(
        module=module,
        version=version,
        load_id=load_id,
        effective_date=effective_date,
    )

    return flow_af_loader.load(
        module=module,
        version=VersionRepository.parse(version),
        effective_date=effective_date,
        keep_dag=keep_dag,
        with_tasks=with_tasks,
        parse_master_flows=parse_master_flows,
        page_size=page_size,
        limit=limit,
    )


class StageFlowFactory:
    def __init__(
        self,
        keep_dag: bool = True,
    ):
        self._keep_dag = keep_dag

    def __call__(
        self,
        dags: Sequence[DAG],
        dags_tasks: Optional[Sequence[Iterable[Task]]],
    ) -> Sequence[StageFlow]:
        if dags_tasks is None:
            dags_tasks = repeat(None, len(dags))
        return [
            self._stage_flow(dag, dag_task) for dag, dag_task in zip(dags, dags_tasks)
        ]

    def _stage_flow(
        self,
        dag: DAG,
        tasks: Optional[Sequence[Task]] = None,
    ) -> StageFlow:
        tags = self._get_tags(dag.tags)
        return StageFlow(
            name=dag.dag_id,
            type=self._get_type(dag.dag_id, tags),
            description=dag.description,
            tags=tags,
            dag=self._get_dag(dag) if self._keep_dag else None,
            tasks=self._get_tasks(tasks),
        )

    @staticmethod
    def _get_type(
        dag_id: str,
        tags: Optional[Sequence[str]],
    ) -> int:
        if dag_id.startswith("wf_"):
            return FlowType.WORK.value
        if dag_id.startswith("wrk_"):
            return FlowType.WORK.value
        if dag_id.startswith("cf_master_"):
            return FlowType.MASTER.value
        if dag_id.startswith("cf_"):
            return FlowType.CONTROL.value
        if dag_id.startswith("sf_"):
            return FlowType.SERVICE.value

        tags = set(tags)
        if "wf" in tags:
            return FlowType.WORK.value
        if "wrk" in tags:
            return FlowType.WORK.value
        if "cf" in tags:
            return FlowType.CONTROL.value
        if "sf" in tags:
            return FlowType.SERVICE.value

        return FlowType.UNDEFINED.value

    @staticmethod
    def _get_tags(tags: Optional[Sequence[Tag]]) -> Optional[Sequence[str]]:
        return [tag.name for tag in tags] if tags is not None else None

    _dag_ignored_attributes = {
        "is_active",
        "is_paused",
        "last_expired",
        "last_parsed_time",
        "last_pickled",
        "next_dagrun",
        "next_dagrun_create_after",
        "next_dagrun_data_interval_end",
        "next_dagrun_data_interval_start",
        "pickle_id",
        "scheduler_lock",
    }

    @classmethod
    def _get_dag(
        cls,
        dag: DAG,
    ) -> Any:
        return {
            name: value
            for name, value in dag.to_dict().items()
            if name not in cls._dag_ignored_attributes
        }

    @classmethod
    def _get_tasks(
        cls,
        tasks: Optional[Sequence[Task]],
    ) -> Any:
        return [cls._get_task(task) for task in tasks] if tasks is not None else None

    _task_ignored_attributes = {
        "end_date",
        "start_date",
    }

    @classmethod
    def _get_task(
        cls,
        task: Task,
    ) -> Any:
        return {
            name: value
            for name, value in task.to_dict().items()
            if name not in cls._task_ignored_attributes
        }


class StageMasterFlowFactory:
    def __call__(
        self,
        dag_id: str,
        tasks: Sequence[Task],
    ) -> Tuple[Sequence[StageMasterFlow], Sequence[StageMasterFlowLink]]:
        def get_group_flow_name(group_id: str) -> str:
            return f"{dag_id}:{group_id}"

        def add_link() -> None:
            master_flow_links.append(
                StageMasterFlowLink(
                    owner_flow_name=group_flow_name,
                    source_flow_name=self._get_flow_name(task_id),
                    target_flow_name=None,
                )
            )

        def add_group_link(source_flow_name: str) -> None:
            master_flow_links.append(
                StageMasterFlowLink(
                    owner_flow_name=dag_id,
                    source_flow_name=source_flow_name,
                    target_flow_name=get_group_flow_name(downstream_group_id),
                )
            )

        def add_deps_links(source_flow_name: str) -> None:
            for downstream_task_id in task.downstream_task_ids:
                downstream_task_id = self._get_task_id(downstream_task_id)
                master_flow_links.append(
                    StageMasterFlowLink(
                        owner_flow_name=group_flow_name,
                        source_flow_name=source_flow_name,
                        target_flow_name=self._get_flow_name(downstream_task_id),
                    )
                )

        master_flows: List[StageMasterFlow] = []
        master_flow_links: List[StageMasterFlowLink] = []

        for task in tasks:
            group_id, task_id = self._get_task_and_group_id(task.task_id)
            group_flow_name = get_group_flow_name(group_id)

            if task.operator_name == "EmptyOperator":
                if task_id == "start":
                    master_flows.append(StageMasterFlow(name=group_flow_name))
                    add_deps_links(source_flow_name=group_flow_name)
                elif group_id == "start" and task_id == "":
                    for downstream_task_id in task.downstream_task_ids:
                        downstream_group_id, downstream_task_id = (
                            self._get_task_and_group_id(downstream_task_id)
                        )
                        if downstream_task_id == "start":
                            add_group_link(source_flow_name=dag_id)
                            break
            elif task.operator_name == "TriggerStep":
                add_link()
            elif task.operator_name == "PythonOperator":
                downstream_task_ids = task.downstream_task_ids
                if task_id == "dummy_ignore_fail" and len(downstream_task_ids) == 1:
                    downstream_task_id = downstream_task_ids[0]
                    downstream_group_id = self._get_task_group_id(downstream_task_id)
                    add_group_link(source_flow_name=group_flow_name)
                elif task_id.startswith("dummy_ignore_fail_trigger_"):
                    flow_name = task_id.removeprefix("dummy_ignore_fail_trigger_")
                    add_deps_links(source_flow_name=flow_name)

        return master_flows, master_flow_links

    @staticmethod
    def is_master_flow(dag: DAG) -> bool:
        path = Path(dag.fileloc or "")
        return path.parent.name == "master_cf"

    @classmethod
    def _get_flow_name(cls, task_id: str) -> str:
        return task_id.removeprefix("trigger_")

    @classmethod
    def _get_task_and_group_id(cls, task_id: str) -> Tuple[str, str]:
        group_id, _, task_id = task_id.partition(".")
        return group_id, task_id

    @classmethod
    def _get_task_group_id(cls, name: str) -> str:
        group_id, _ = cls._get_task_and_group_id(name)
        return group_id

    @classmethod
    def _get_task_id(cls, name: str) -> str:
        _, task_id = cls._get_task_and_group_id(name)
        return task_id
