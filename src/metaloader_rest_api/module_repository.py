from sqlalchemy import text
from sqlalchemy.exc import NoResultFound
from sqlalchemy.orm import Session
from structlog.stdlib import Bound<PERSON>ogger, get_logger

from metaloader_rest_api.common_model import LAST_DATE
from metaloader_rest_api.loggable import Loggable

logger = get_logger(__name__)


class ModuleRepository(Loggable):
    def __init__(
        self,
        session: Session,
        log: BoundLogger = logger,
    ):
        self._log = log

        self._session = session

    def get_id(
        self,
        name: str,
    ) -> int:
        try:
            return self._session.scalars(
                statement=text("""
                   SELECT code_delivery_rk
                     FROM dict.dict_code_delivery
                    WHERE code_delivery_cd  = :code_delivery_cd
                      AND deleted_flg IS FALSE
                """),
                params={
                    "code_delivery_cd": name,
                    "effective_to_dttm": LAST_DATE,
                },
            ).one()
        except NoResultFound:
            self._log.exception("module_not_found", module=name)
            raise


def get_module_id(
    session: Session,
    name: str,
    log: BoundLogger = logger,
) -> int:
    module_repository = ModuleRepository(session, log)
    return module_repository.get_id(name)
