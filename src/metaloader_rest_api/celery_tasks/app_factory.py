from functools import partial
from typing import Any, Mapping, Protocol, Sequence, Type
from uuid import UUID

from celery import Celery
from kombu.utils.json import register_type
from pydantic import BaseModel, RedisDsn

from metaloader_rest_api.ceh_module import ceh_module_api
from metaloader_rest_api.ceh_service import ceh_service_api
from metaloader_rest_api.data_model import data_model_api
from metaloader_rest_api.flow_processor import flow_processor_api


class CelerySettings(Protocol):
    celery_broker_url: RedisDsn


def get_celery_app(settings: CelerySettings, name: str = "tasks") -> Celery:
    from . import celeryconfig

    app = Celery(main=name, broker=settings.celery_broker_url.unicode_string())
    app.config_from_object(celeryconfig, force=True)

    register_types()

    flow_processor_api.registry_flow_processors()

    return app


def register_types() -> None:
    register_uuid_type()
    register_pydantic_model_types()


def register_uuid_type() -> None:
    register_type(
        UUID,
        marker="UUID",
        encoder=lambda value: str(value),
        decoder=lambda value: UUID(value),
    )


PYDANTIC_MODEL_TYPES = (
    data_model_api.LoadDataModelParams,
    data_model_api.LoadLogicalDataModelParams,
    flow_processor_api.ProcessFlowsParams,
    ceh_service_api.LoadServicesParam,
    ceh_module_api.LoadModulesParam,
)


def register_pydantic_model_types(
    model_types: Sequence[Type[BaseModel]] = PYDANTIC_MODEL_TYPES,
) -> None:
    for model_type in model_types:
        register_type(
            model_type,
            marker=pydantic_model_marker(model_type),
            encoder=pydantic_model_encoder,
            decoder=partial(pydantic_model_decoder, model_type),
        )


def pydantic_model_marker(model_type: Type[BaseModel]) -> str:
    return f"{model_type.__module__}.{model_type.__qualname__}"


def pydantic_model_encoder(
    model: BaseModel,
) -> Mapping[str, Any]:
    return model.model_dump(mode="json")


def pydantic_model_decoder(
    model_type: Type[BaseModel],
    data: Mapping[str, Any],
) -> BaseModel:
    return model_type(**data)
