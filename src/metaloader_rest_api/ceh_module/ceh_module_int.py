from datetime import datetime
from typing import Iterator
from uuid import UUID

from sqlalchemy.orm import Session
from structlog.stdlib import <PERSON><PERSON><PERSON><PERSON><PERSON>, get_logger

from metaloader_rest_api.ceh_module.ceh_module_model import CehModule
from metaloader_rest_api.ceh_module.ceh_module_repository import (
    StageModuleRepository,
)
from metaloader_rest_api.common_repository import SessionResource, get_table_id

_DEFAULT_LOGGER = get_logger(__name__)


def load_modules(
    session: Session,
    load_id: UUID,
    effective_date: datetime,
    modules: Iterator[CehModule],
    log: BoundLogger = _DEFAULT_LOGGER,
):
    log = log.bind(action="load_modules")
    log.info("begin")

    table_id = get_table_id(load_id)

    repository = StageModuleRepository(
        session=session,
        effective_date=effective_date,
        table_id=table_id,
        log=log,
    )

    session_resource = SessionResource(session)

    with repository, session_resource:
        repository.load_all(modules)

    log.info("end")
