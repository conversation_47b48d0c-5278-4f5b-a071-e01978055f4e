from datetime import datetime
from typing import Optional, Type
from uuid import UUI<PERSON>

from sqlalchemy import text
from sqlalchemy.orm import Session
from structlog.stdlib import get_logger

from metaloader_rest_api.common_model import LAST_DATE
from metaloader_rest_api.common_repository import StageBatchRepository
from metaloader_rest_api.loader_info_system.models.base import SystemInfoLoadRepository

logger = get_logger(__name__)


def load_stage_data(
    session: Session,
    load_id: UUID,
    new_data: list[dict],
    repository_class: Type[StageBatchRepository],
) -> None:
    """Loads data into a staging table."""
    with repository_class(
        session=session,
        table_id=load_id.hex,
        page_size=100,
    ) as repository:
        repository.load(new_data)
        session.commit()


def load_system_data(
    session: Session,
    new_data: list[dict],
    repository_class: Type[SystemInfoLoadRepository],
) -> None:
    """Inserts data in the target table."""
    with repository_class(session=session) as repository:
        repository.load(new_data)
        session.commit()


def update_system_data(
    session: Session,
    new_data: list[dict],
    repository_class: Type[SystemInfoLoadRepository],
) -> None:
    """Updates data in the target table."""
    with repository_class(session=session) as repository:
        repository.update(new_data)
        session.commit()


def get_new_record_bridge_source(stage_table: str) -> str:
    """Generates a subquery for new records in the metamodel.bridge_source."""
    return f"""
        SELECT
            s.source_cd,
            COALESCE(m.source_rk, nextval('metamodel.md_seq')) AS source_rk,
            s.parent_source_cd,
            s.ris_src_id,
            s.ris_src_code,
            s.short_cd,
            s.source_desc,
            s.data_layer_rk,
            :effective_from_dttm AS effective_from_dttm,
            :effective_to_dttm AS effective_to_dttm,
            FALSE AS deleted_flg
        FROM {stage_table} s
            LEFT JOIN metamodel.bridge_source m
                ON s.source_cd = m.source_cd
        WHERE m.source_rk IS NULL
    """


def get_updated_record_bridge_source(stage_table: str) -> str:
    """Generates a query for updated records in the metamodel.bridge_source."""
    return f"""
        SELECT
            s.source_cd,
            m.source_rk,
            s.parent_source_cd,
            s.ris_src_id,
            s.ris_src_code,
            s.short_cd,
            s.source_desc,
            s.data_layer_rk,
            :effective_from_dttm AS effective_from_dttm,
            :effective_to_dttm AS effective_to_dttm,
            FALSE AS deleted_flg
        FROM {stage_table} s
            INNER JOIN metamodel.bridge_source m
                ON s.source_cd = m.source_cd
            LEFT JOIN metamodel.bridge_source parent_m
                ON s.parent_source_cd = parent_m.source_cd
        WHERE (
            s.ris_src_id <> m.ris_src_id
            OR s.ris_src_code <> m.ris_src_code
            OR s.source_desc IS DISTINCT FROM m.source_desc
            OR s.data_layer_rk IS DISTINCT FROM m.data_layer_rk
            OR parent_m.source_rk IS DISTINCT FROM m.parent_source_rk
        )
            AND m.effective_to_dttm = :effective_to_dttm
    """


def get_closed_record_bridge_source(stage_table: str) -> str:
    """Generates a query for logically deleted records in the metamodel.bridge_source."""
    return f"""
        SELECT
            m.source_cd,
            m.source_rk,
            s.parent_source_cd,
            m.ris_src_id,
            m.ris_src_code,
            m.short_cd,
            m.source_desc,
            m.data_layer_rk,
            :effective_from_dttm as effective_from_dttm,
            :effective_to_dttm AS effective_to_dttm,
            TRUE AS deleted_flg
        FROM metamodel.bridge_source m
            LEFT JOIN {stage_table} s
                ON m.source_cd = s.source_cd
        WHERE s.source_cd IS NULL
            AND m.effective_to_dttm = :effective_to_dttm
            AND m.deleted_flg = FALSE
    """


def get_recover_record_bridge_source(stage_table: str) -> str:
    """Generates a query for recovering logically deleted records in the metamodel.bridge_source."""
    return f"""
        SELECT
            s.source_cd,
            m.source_rk,
            s.parent_source_cd,
            s.ris_src_id,
            s.ris_src_code,
            s.short_cd,
            s.source_desc,
            s.data_layer_rk,
            :effective_from_dttm AS effective_from_dttm,
            :effective_to_dttm AS effective_to_dttm,
            FALSE AS deleted_flg
        FROM {stage_table} s
            INNER JOIN metamodel.bridge_source m
                ON s.source_cd = m.source_cd
        WHERE m.deleted_flg = TRUE
            AND m.effective_to_dttm = :effective_to_dttm
    """


def prepare_query_bridge_source(load_id: str, without_parent: bool) -> str:
    """Prepares data for insertion into the bridge source table."""
    logger.info(f"Preparing query for bridge_source")
    stage_table = f"stg.stg_bridge_source_{load_id}"

    new_records = get_new_record_bridge_source(stage_table)
    updated_records = get_updated_record_bridge_source(stage_table)
    deleted_records = get_closed_record_bridge_source(stage_table)
    recovered_records = get_recover_record_bridge_source(stage_table)

    cte_source = f"""
        {new_records}
        UNION
        {updated_records}
        UNION
        {deleted_records}
        UNION
        {recovered_records}
    """

    return f"""
        WITH cte as ({cte_source})
        SELECT
            cte.source_rk,
            cte.source_cd,
            meta.source_rk AS parent_source_rk,
            cte.ris_src_id,
            cte.ris_src_code,
            cte.short_cd,
            cte.source_desc,
            cte.data_layer_rk,
            cte.effective_from_dttm,
            cte.effective_to_dttm,
            cte.deleted_flg
        FROM cte
            LEFT JOIN (
                       SELECT
                           source_cd,
                           source_rk
                       FROM metamodel.bridge_source
                       WHERE effective_to_dttm = :effective_to_dttm
                       ) meta 
                            ON cte.parent_source_cd = meta.source_cd
        {f"WHERE cte.parent_source_cd IS NULL" if without_parent else ""};
        """


def execute_query_text(
    session: Session, query: str, effective_from_dttm: Optional[datetime] = None
) -> list[dict]:
    """Executes a raw SQL query and returns the results as a list of dictionaries."""

    params = {}
    if effective_from_dttm is not None:
        params["effective_from_dttm"] = effective_from_dttm

    params["effective_to_dttm"] = LAST_DATE

    result = session.execute(statement=text(query), params=params).mappings()
    return [dict(row) for row in result]
