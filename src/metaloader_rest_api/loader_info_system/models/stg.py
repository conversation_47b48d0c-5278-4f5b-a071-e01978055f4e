from typing import Optional, TypedDict

from sqlalchemy.orm import Session

from metaloader_rest_api.common_repository import StageBatchRepository


class StageBridgeSource(TypedDict):
    source_cd: str
    parent_source_cd: Optional[str]
    ris_src_id: str
    ris_src_code: str
    short_cd: str
    source_desc: Optional[str]
    data_layer_rk: Optional[int]


class StageBridgeSourceRepository(StageBatchRepository[StageBridgeSource]):
    def __init__(
        self,
        session: Session,
        table_id: str,
        page_size: int = StageBatchRepository.PAGE_SIZE_DEFAULT,
    ):
        table = "stg_bridge_source"
        super().__init__(
            session,
            table,
            table_id,
            self._create_statement,
            self._load_statement,
            page_size,
        )

    @staticmethod
    def _create_statement(table: str) -> str:
        return f"""
            CREATE TABLE {table} (
                source_cd         TEXT NOT NULL,
                parent_source_cd  TEXT,
                ris_src_id        TEXT NOT NULL,
                ris_src_code      TEXT NOT NULL,
                short_cd          TEXT,
                source_desc       TEXT,
                data_layer_rk     BIGINT
            );
        """

    @staticmethod
    def _load_statement(table: str) -> str:
        return f"""
            INSERT INTO {table} (
                source_cd,
                parent_source_cd,
                ris_src_id,
                ris_src_code,
                short_cd,
                source_desc,
                data_layer_rk
            ) VALUES (
                UPPER(:source_cd),
                :parent_source_cd,
                :ris_src_id,
                :ris_src_code,
                :short_cd,
                :source_desc,
                :data_layer_rk
            )
        """
