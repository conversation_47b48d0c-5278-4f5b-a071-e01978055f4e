from typing import Optional, TypedDict

from sqlalchemy.orm import Session

from metaloader_rest_api.loader_info_system.models.base import (
    SystemInfoLoadRepository,
)


class BridgeSource(TypedDict):
    source_rk: int
    parent_source_rk: Optional[int]
    source_cd: str
    ris_src_id: str
    ris_src_code: str
    short_cd: Optional[str]
    source_desc: Optional[str]
    data_layer_rk: Optional[int]
    effective_from_dttm: str
    effective_to_dttm: str
    deleted_flg: bool


class BridgeSourceRepository(SystemInfoLoadRepository[BridgeSource]):
    def __init__(
        self,
        session: Session,
    ):
        table = "metamodel.bridge_source"
        super().__init__(
            session,
            table,
            self._load_statement,
            self._update_statement,
        )

    @staticmethod
    def _load_statement(table: str):
        return f"""
            INSERT INTO {table} (
                source_rk,
                source_cd,
                parent_source_rk,
                ris_src_id,
                ris_src_code,
                short_cd,
                source_desc,
                data_layer_rk,
                effective_from_dttm,
                effective_to_dttm,
                deleted_flg
            )
            VALUES (
                :source_rk,
                :source_cd,
                :parent_source_rk,
                :ris_src_id,
                :ris_src_code,
                :short_cd,
                :source_desc,
                :data_layer_rk,
                :effective_from_dttm,
                :effective_to_dttm,
                :deleted_flg
            );
        """

    @staticmethod
    def _update_statement(table: str) -> str:
        return f"""
            UPDATE {table}
                SET effective_to_dttm = :effective_from_dttm 
            WHERE source_rk = :source_rk
                AND effective_to_dttm = :effective_to_dttm;
        """
