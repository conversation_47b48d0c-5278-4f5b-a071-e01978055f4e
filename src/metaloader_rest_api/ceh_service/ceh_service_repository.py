from datetime import datetime

from sqlalchemy import text
from sqlalchemy.orm import Session
from structlog.stdlib import <PERSON><PERSON><PERSON><PERSON><PERSON>, get_logger

from metaloader_rest_api import etl
from metaloader_rest_api.ceh_service.ceh_service_model import CehService
from metaloader_rest_api.common_model import LAST_DATE
from metaloader_rest_api.common_repository import (
    BufferedStageBatchRepository,
)

_DEFAULT_LOGGER = get_logger(__name__)


class StageServiceRepository(BufferedStageBatchRepository[CehService]):
    def __init__(
        self,
        session: Session,
        effective_date: datetime,
        table_id: str,
        page_size: int = BufferedStageBatchRepository.PAGE_SIZE_DEFAULT,
        log: BoundLogger = _DEFAULT_LOGGER,
    ):
        table = "mart_service"
        super().__init__(
            session,
            table,
            table_id,
            self._create_statement,
            self._load_statement,
            page_size,
            log,
        )
        self._effective_date = effective_date
        self._table_id = table_id

    @property
    def effective_date(self) -> datetime:
        return self._effective_date

    @effective_date.setter
    def effective_date(self, value: datetime) -> None:
        self._effective_date = value

    @property
    def table_id(self) -> str:
        return self._table_id

    @table_id.setter
    def table_id(self, value: str) -> None:
        self._table_id = value

    def __exit__(self, exc_type, exc_value, traceback):
        super().__exit__(exc_type, exc_value, traceback)

        if exc_type is None:
            self.load_bridge()
            self.load_link()

    @staticmethod
    def _create_statement(table: str) -> str:
        return f"""
            CREATE TABLE {table} (
                service_cd        TEXT
              , service_type_cd   TEXT
              , service_name      TEXT
              , service_desc      TEXT
              , service_host_name TEXT
              , service_url       TEXT
              , service_alt_url   TEXT
              , environment_cd    TEXT
              , code_delivery_cd  TEXT
              , ris_src_id        TEXT
              , ris_src_code      TEXT
            )
        """

    @staticmethod
    def _load_statement(table: str) -> str:
        return f"""
            INSERT INTO {table} (service_cd
                               , service_type_cd
                               , service_name
                               , service_desc                               
                               , service_host_name
                               , service_url
                               , service_alt_url
                               , environment_cd
                               , code_delivery_cd
                               , ris_src_id
                               , ris_src_code)
                 VALUES (:service_cd
                       , LOWER(:service_type_cd)
                       , :service_name
                       , :service_desc                       
                       , :service_host_name
                       , :service_url
                       , :service_alt_url
                       , LOWER(:environment_cd)
                       , :code_delivery_cd
                       , :ris_src_id
                       , :ris_src_code)
        """

    def load_bridge(self) -> None:
        log = self._log.bind(table="bridge_service")
        log.info("begin")

        merge_stage = f"stg.bridge_service_{self.table_id}"

        log.info("create stage", table=merge_stage)
        self._session.execute(text(f"DROP TABLE IF EXISTS {merge_stage}"))
        self._session.execute(
            text(f"""
                CREATE TABLE {merge_stage} (
                    service_cd        TEXT
                  , service_type_rk   SMALLINT
                  , service_name      TEXT
                  , service_desc      TEXT
                  , service_host_name TEXT
                  , service_url       TEXT
                  , service_alt_url   TEXT
                  , environment_rk    SMALLINT
                )
            """)
        )

        self._session.execute(
            statement=text(f"""
                INSERT INTO {merge_stage} (service_cd
                                         , service_type_rk
                                         , service_name
                                         , service_desc
                                         , service_host_name
                                         , service_url
                                         , service_alt_url
                                         , environment_rk)
                     SELECT t.service_cd                     service_cd
                          , COALESCE(st.service_type_rk, -1) service_type_rk
                          , t.service_name                   service_name
                          , t.service_desc                   service_desc
                          , t.service_host_name              service_host_name
                          , t.service_url                    service_url
                          , t.service_alt_url                service_alt_url
                          , COALESCE(e.environment_rk,   -1) environment_rk
                       FROM {self.table}                           t
                  LEFT JOIN dict.dict_service_type                 st
                         ON st.service_type_cd = t.service_type_cd
                  LEFT JOIN dict.dict_environment                  e
                         ON e.environment_cd = t.environment_cd
            """)
        )

        log.info("merge")
        etl.merge(
            session=self._session,
            version_id=None,
            effective_date=self.effective_date,
            table="metamodel.bridge_service",
            stage=merge_stage,
            primary_key="service_rk",
            keys=[
                "service_cd",
            ],
            values=[
                "service_type_rk",
                "service_name",
                "service_desc",
                "service_host_name",
                "service_url",
                "service_alt_url",
                "environment_rk",
            ],
            log=log,
        )
        log.info("end")

    def load_link(self) -> None:
        log = self._log.bind(table="link_service_delivery")
        log.info("begin")

        table = "metamodel.link_service_delivery"

        self._session.execute(text(f"TRUNCATE TABLE {table}"))
        self._session.execute(
            statement=text(f"""
                INSERT INTO {table} (service_rk
                                   , code_delivery_rk)
                     SELECT s.service_rk        service_rk
                          , cd.code_delivery_rk code_delivery_rk
                       FROM {self.table}                            t
                       JOIN dict.dict_code_delivery                 cd
                         ON cd.code_delivery_cd  = t.code_delivery_cd
                        AND cd.deleted_flg IS FALSE                              
                       JOIN metamodel.bridge_service                s
                         ON s.service_cd        = t.service_cd
                        AND s.effective_to_dttm = :effective_to_dttm          
                        AND s.deleted_flg IS FALSE       

            """),
            params={
                "effective_to_dttm": LAST_DATE,
            },
        )
        log.info("end")
