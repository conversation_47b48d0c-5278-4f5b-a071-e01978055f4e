import os
import uuid
from datetime import datetime, timezone

from data_model.test_utils import TableViewTest
from metaloader_rest_api.common_model import LAST_DATE
from metaloader_rest_api.flow_param.flow_param_processor import FlowParamProcessor
from metaloader_rest_api.flow_resource.flow_resource_model import FlowResourceType
from metaloader_rest_api.flow_resource.flow_resource_processor import (
    FlowResourceProcessor,
)
from metaloader_rest_api.http_file_provider.http_file_provider import HttpFileProvider
from metaloader_rest_api.version_repository import VersionRepository
from metaloader_rest_api.yaml.yaml_loader import load_yaml
from pytest import fixture
from sqlalchemy import bindparam, text
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.orm import Session


def test_process_with_parameter(
    db_session,
    session_resource,
    http_file_provider,
    core_module_id,
    core_version_id,
    truncate_tables,
    flow_test_21,
):
    flow_id, flow_name, flow_content = flow_test_21
    # fmt: off
    bridge_table = TableViewTest(
        (
            "table_rk",
            "domain_rk",
            "data_layer_rk",
            "source_rk",
            "schema_name",
            "table_name",
            "table_type_rk",
            "table_distribution_type_rk",
            "is_dict_flg",
            "is_map_flg",
            "is_hub_flg",
            "is_sal_flg",
            "is_mart_flg",
            "is_bridge_flg",
            "is_link_flg",
            "is_temporal_flg",
            "is_accessor_flg",
            "version_scd_type_rk",
            "history_scd_type_rk",
            "is_deprecated_flg",
            "version_rk",
            "effective_from_dttm",
            "effective_to_dttm",
            "deleted_flg",
        ),
        (
            (99, -1, -1, -1, "test_schema_2", "test_table_2", -1, -1, False, False, False, False, False, True, False, False, False, -1, -1, False, 1, datetime(2022, 1, 1, tzinfo=timezone.utc), LAST_DATE, False),
        ),
        table_name="metamodel.bridge_table",
    )
    bridge_table.insert(db_session)

    bridge_resource = TableViewTest(
        (
            "resource_rk",
            "resource_cd",
            "is_readonly_flg",
            "is_maintenance_flg",
            "resource_json",
            "version_rk",
            "effective_from_dttm",
            "effective_to_dttm",
            "deleted_flg",
        ),
        (
            (1, 'ceh.test_schema_1.test_table_2', False, False, "", 1, datetime(2022, 1, 1, tzinfo=timezone.utc), LAST_DATE, False),
        ),
        table_name="metamodel.bridge_resource",
        bind_params=[bindparam("resource_json", type_=JSONB(none_as_null=True))]
    )
    bridge_resource.insert(db_session)
    # fmt: on

    effective_date = datetime(2024, 1, 1, tzinfo=timezone.utc)
    load_id = uuid.uuid4()

    param_processor = FlowParamProcessor(
        db_session,
        http_file_provider,
        load_id,
        core_module_id,
        core_version_id,
        effective_date,
    )

    resource_processor = FlowResourceProcessor(
        db_session,
        http_file_provider,
        load_id,
        core_module_id,
        core_version_id,
        effective_date,
    )

    with session_resource, param_processor:
        param_processor.process(flow_id, flow_name, flow_content)

    # link_flow_parameter_pass
    expected = TableViewTest(
        (
            "flow_rk",
            "flow_parameter_name",
            "data_type_rk",
            "default_value_json",
            "effective_from_dttm",
            "effective_to_dttm",
            "deleted_flg",
        ),
        (
            (
                flow_id,
                "algorithm_name",
                16,
                "test_alg_test_table_21",
                effective_date,
                LAST_DATE,
                False,
            ),
            (flow_id, "common_version_id", 3, 123, effective_date, LAST_DATE, False),
            (flow_id, "init_load_flg", 1, False, effective_date, LAST_DATE, False),
            (flow_id, "src_version_id", 3, 223, effective_date, LAST_DATE, False),
        ),
    )

    actual = TableViewTest.table_rows_for_query(
        db_session,
        """
              SELECT l.flow_rk
                   , b.flow_parameter_name
                   , b.data_type_rk
                   , l.default_value_json
                   , l.effective_from_dttm
                   , l.effective_to_dttm
                   , l.deleted_flg
                FROM metamodel.link_flow_parameter l
                JOIN metamodel.bridge_flow_parameter b
                  ON b.flow_parameter_rk = l.flow_parameter_rk
            ORDER BY flow_parameter_name;
    """,
    )

    assert expected == actual

    with session_resource, resource_processor:
        resource_processor.process(flow_id, flow_name, flow_content)

    # bridge_etl_algorithm
    expected = TableViewTest(
        (
            "etl_algorithm_cd",
            "code_delivery_rk",
            "version_rk",
            "effective_from_dttm",
            "effective_to_dttm",
            "deleted_flg",
        ),
        (
            (
                "test_alg_test_table_21",
                core_module_id,
                core_version_id,
                effective_date,
                LAST_DATE,
                False,
            ),
        ),
        table_name="metamodel.bridge_etl_algorithm",
    )
    actual = TableViewTest.table_rows_for_table_view(
        db_session, expected, ("etl_algorithm_rk",)
    )

    assert expected == actual

    # link_flow_resource
    # fmt: off
    expected = TableViewTest(
        (
            "flow_rk",
            "resource_rk",
            "flow_resource_link_type_rk",
            "etl_algorithm_cd",
            "table_rk",
            "version_rk",
            "effective_from_dttm",
            "effective_to_dttm",
            "deleted_flg",
        ),
        (
            (flow_id, 1, FlowResourceType.TARGET, "test_alg_test_table_21", 99, core_version_id, effective_date, LAST_DATE, False),
        ),
        table_name="metamodel.bridge_etl_algorithm",
    )
    # fmt: on

    actual = TableViewTest.table_rows_for_query(
        db_session,
        """
            SELECT fr.flow_rk
                 , fr.resource_rk
                 , fr.flow_resource_link_type_rk
                 , ef.etl_algorithm_cd
                 , fr.table_rk
                 , fr.version_rk
                 , fr.effective_from_dttm
                 , fr.effective_to_dttm
                 , fr.deleted_flg
              FROM metamodel.link_flow_resource fr
              JOIN metamodel.bridge_resource r
                ON fr.resource_rk = r.resource_rk
              JOIN metamodel.bridge_etl_algorithm ef
                ON ef.etl_algorithm_rk = fr.etl_algorithm_rk
          ORDER BY fr.flow_rk
                 , fr.resource_rk;
        """,
    )

    assert expected == actual


def add_version_id(
    db_session,
    session_resource,
    module_id: int,
) -> int:
    with session_resource:
        version_repository = VersionRepository(db_session)
        return version_repository.put(
            module_id,
            major=1,
            minor=2,
            fix=3,
        )


def add_module_id(
    db_session,
    name: str,
    id: int,
) -> int:
    db_session.execute(
        statement=text("""
            INSERT INTO dict.dict_code_delivery(code_delivery_rk
                                              , code_delivery_cd
                                              , code_delivery_name
                                              , is_ddl_flg
                                              , is_flow_flg
                                              , is_config_flg
                                              , deleted_flg)
            VALUES (:code_delivery_rk
                  , :code_delivery_name
                  , :code_delivery_name
                  , FALSE
                  , TRUE
                  , FALSE
                  , FALSE)
        """),
        params={
            "code_delivery_rk": id,
            "code_delivery_name": name,
        },
    )
    db_session.commit()
    return id


def load_flow_data(path, flow_id, file_name):
    yaml_file_path = os.path.join(path, f"{file_name}.yaml")

    with open(yaml_file_path, "r", encoding="utf-8") as file:
        return flow_id, file_name, load_yaml(file.read())


@fixture(scope="function")
def http_file_provider(local_file_server_url) -> HttpFileProvider:
    return HttpFileProvider(url=local_file_server_url)


@fixture(scope="function", autouse=True)
def truncate_tables(db_session: Session):
    db_session.execute(text("TRUNCATE dict.dict_code_delivery"))
    db_session.execute(text("TRUNCATE metamodel.bridge_version"))
    db_session.execute(text("TRUNCATE metamodel.bridge_resource"))
    db_session.execute(text("TRUNCATE metamodel.bridge_table"))
    db_session.execute(text("TRUNCATE metamodel.bridge_etl_algorithm"))
    db_session.execute(text("TRUNCATE metamodel.link_flow_parameter"))
    db_session.execute(text("TRUNCATE metamodel.link_flow_parameter_pass"))
    db_session.execute(text("TRUNCATE metamodel.link_flow_resource"))
    db_session.commit()


@fixture(scope="function")
def core_version_id(
    db_session,
    session_resource,
    core_module_id,
) -> int:
    return add_version_id(db_session, session_resource, core_module_id)


@fixture(scope="function")
def dm_version_id(
    db_session,
    session_resource,
    dm_module_id,
) -> int:
    return add_version_id(db_session, session_resource, dm_module_id)


@fixture(scope="function")
def core_module_id(db_session) -> int:
    return add_module_id(db_session, "core", 1)


@fixture(scope="function")
def dm_module_id(db_session) -> int:
    return add_module_id(db_session, "dm", 2)


@fixture(scope="function")
def flow_test_21(yaml_rdv_idl_path):
    return load_flow_data(yaml_rdv_idl_path, 21, "flow_test_21")
