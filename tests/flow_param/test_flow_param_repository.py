from datetime import datetime, timezone

from metaloader_rest_api.common_model import LAST_DATE
from metaloader_rest_api.common_repository import SessionResource
from metaloader_rest_api.flow_param.flow_param_repository import FlowParamRepository
from metaloader_rest_api.module_repository import get_module_id
from metaloader_rest_api.version_repository import VersionRepository
from pytest import fixture
from sqlalchemy import text
from sqlalchemy.orm import Session


def test_flow_param_repository(
    db_session: Session,
    version_repository,
    session_resource,
    truncate_tables,
    insert_dict_code_delivery,
):
    effective_date_1 = datetime(2024, 1, 1, 0, 0, 0, tzinfo=timezone.utc)
    module_id = get_module_id(db_session, "dm")

    version_id_1 = version_repository.put(
        module_id=module_id,
        major=999,
        minor=999,
        fix=999,
        delivery_date=effective_date_1,
    )
    db_session.commit()

    flow_param_repository = FlowParamRepository(
        session=db_session,
        module_id=module_id,
        version_id=version_id_1,
        effective_date=effective_date_1,
        table_id="test",
    )

    # 1. Добавление одного потока с одним параметром

    flow_param_1 = {
        "flow_id": 1,
        "flow_name": "flow_test_1",
        "name": "parameter_test_1",
        "data_type": 19,
        "value": None,
    }

    with session_resource, flow_param_repository:
        flow_param_repository.load_one(flow_param_1)

    link_rows = link_flow_parameter_rows(db_session)
    expected_link_rows = [
        {
            "flow_rk": flow_param_1["flow_id"],
            "flow_parameter_cd": flow_param_1["name"],
            "data_type_rk": flow_param_1["data_type"],
            "default_value_json": flow_param_1["value"],
            "version_rk": version_id_1,
            "effective_from_dttm": effective_date_1,
            "effective_to_dttm": LAST_DATE,
            "deleted_flg": False,
        },
    ]

    assert link_rows == expected_link_rows

    bridge_rows = bridge_flow_parameter_rows(db_session)
    expected_bridge_rows = [
        {
            "flow_parameter_cd": flow_param_1["name"],
            "flow_parameter_name": flow_param_1["name"],
            "data_type_rk": flow_param_1["data_type"],
        },
    ]

    assert bridge_rows == expected_bridge_rows

    # 2. Добавление потока из другого модуля (потоки предыдущего модуля не должны удалиться)

    flow_param_2 = {
        "flow_id": 2,
        "flow_name": "flow_test_1",
        "name": "parameter_test_1",
        "data_type": 19,
        "value": None,
    }

    effective_date_2 = datetime(2024, 2, 1, 0, 0, 0, tzinfo=timezone.utc)
    module_id = get_module_id(db_session, "core")

    version_id_2 = version_repository.put(
        module_id=module_id,
        major=999,
        minor=999,
        fix=999,
        delivery_date=effective_date_2,
    )
    db_session.commit()

    flow_param_repository.module_id = module_id
    flow_param_repository.version_id = version_id_2
    flow_param_repository.effective_date = effective_date_2

    with session_resource, flow_param_repository:
        flow_param_repository.load_one(flow_param_2)

    link_rows = link_flow_parameter_rows(db_session)
    expected_link_rows = [
        {
            "flow_rk": flow_param_1["flow_id"],
            "flow_parameter_cd": flow_param_1["name"],
            "data_type_rk": flow_param_1["data_type"],
            "default_value_json": flow_param_1["value"],
            "version_rk": version_id_1,
            "effective_from_dttm": effective_date_1,
            "effective_to_dttm": LAST_DATE,
            "deleted_flg": False,
        },
        {
            "flow_rk": flow_param_2["flow_id"],
            "flow_parameter_cd": flow_param_2["name"],
            "data_type_rk": flow_param_2["data_type"],
            "default_value_json": flow_param_2["value"],
            "version_rk": version_id_2,
            "effective_from_dttm": effective_date_2,
            "effective_to_dttm": LAST_DATE,
            "deleted_flg": False,
        },
    ]

    assert link_rows == expected_link_rows

    bridge_rows = bridge_flow_parameter_rows(db_session)

    assert bridge_rows == expected_bridge_rows

    # 3. Повторное добавление этой же записи (результат должен остаться прежним)

    effective_date_3 = datetime(2024, 3, 1, 0, 0, 0, tzinfo=timezone.utc)

    version_id_3 = version_repository.put(
        module_id=module_id,
        major=999,
        minor=999,
        fix=999,
        delivery_date=effective_date_3,
    )
    db_session.commit()

    flow_param_repository.module_id = module_id
    flow_param_repository.version_id = version_id_3
    flow_param_repository.effective_date = effective_date_3

    with session_resource, flow_param_repository:
        flow_param_repository.load_one(flow_param_2)

    link_rows = link_flow_parameter_rows(db_session)

    assert link_rows == expected_link_rows

    bridge_rows = bridge_flow_parameter_rows(db_session)

    assert bridge_rows == expected_bridge_rows

    # 4. Изменение дефолтного значения для параметра потока (должна нарезаться история в link_flow_parameter)

    flow_param_4 = {
        "flow_id": 2,
        "flow_name": "flow_test_1",
        "name": "parameter_test_1",
        "data_type": 19,
        "value": '{"123"}',
    }

    effective_date_4 = datetime(2024, 4, 1, 0, 0, 0, tzinfo=timezone.utc)

    version_id_4 = version_repository.put(
        module_id=module_id,
        major=999,
        minor=999,
        fix=999,
        delivery_date=effective_date_4,
    )
    db_session.commit()

    flow_param_repository.module_id = module_id
    flow_param_repository.version_id = version_id_4
    flow_param_repository.effective_date = effective_date_4

    with session_resource, flow_param_repository:
        flow_param_repository.load_one(flow_param_4)

    link_rows = link_flow_parameter_rows(db_session)
    expected_link_rows = [
        {
            "flow_rk": flow_param_1["flow_id"],
            "flow_parameter_cd": flow_param_1["name"],
            "data_type_rk": flow_param_1["data_type"],
            "default_value_json": flow_param_1["value"],
            "version_rk": version_id_1,
            "effective_from_dttm": effective_date_1,
            "effective_to_dttm": LAST_DATE,
            "deleted_flg": False,
        },
        {
            "flow_rk": flow_param_2["flow_id"],
            "flow_parameter_cd": flow_param_2["name"],
            "data_type_rk": flow_param_2["data_type"],
            "default_value_json": flow_param_2["value"],
            "version_rk": version_id_4,
            "effective_from_dttm": effective_date_2,
            "effective_to_dttm": effective_date_4,
            "deleted_flg": False,
        },
        {
            "flow_rk": flow_param_4["flow_id"],
            "flow_parameter_cd": flow_param_4["name"],
            "data_type_rk": flow_param_4["data_type"],
            "default_value_json": flow_param_4["value"],
            "version_rk": version_id_4,
            "effective_from_dttm": effective_date_4,
            "effective_to_dttm": LAST_DATE,
            "deleted_flg": False,
        },
    ]

    assert link_rows == expected_link_rows

    bridge_rows = bridge_flow_parameter_rows(db_session)

    assert bridge_rows == expected_bridge_rows

    # 5. Добавление ещё одного параметра потоку

    flow_param_5 = {
        "flow_id": 2,
        "flow_name": "flow_test_1",
        "name": "parameter_test_2",
        "data_type": 1,
        "value": None,
    }

    effective_date_5 = datetime(2024, 5, 1, 0, 0, 0, tzinfo=timezone.utc)

    version_id_5 = version_repository.put(
        module_id=module_id,
        major=999,
        minor=999,
        fix=999,
        delivery_date=effective_date_5,
    )
    db_session.commit()

    flow_param_repository.version_id = version_id_5
    flow_param_repository.effective_date = effective_date_5

    with session_resource, flow_param_repository:
        flow_param_repository.load_one(flow_param_4)
        flow_param_repository.load_one(flow_param_5)

    link_rows = link_flow_parameter_rows(db_session)
    expected_link_rows = [
        {
            "flow_rk": flow_param_1["flow_id"],
            "flow_parameter_cd": flow_param_1["name"],
            "data_type_rk": flow_param_1["data_type"],
            "default_value_json": flow_param_1["value"],
            "version_rk": version_id_1,
            "effective_from_dttm": effective_date_1,
            "effective_to_dttm": LAST_DATE,
            "deleted_flg": False,
        },
        {
            "flow_rk": flow_param_2["flow_id"],
            "flow_parameter_cd": flow_param_2["name"],
            "data_type_rk": flow_param_2["data_type"],
            "default_value_json": flow_param_2["value"],
            "version_rk": version_id_4,
            "effective_from_dttm": effective_date_2,
            "effective_to_dttm": effective_date_4,
            "deleted_flg": False,
        },
        {
            "flow_rk": flow_param_4["flow_id"],
            "flow_parameter_cd": flow_param_4["name"],
            "data_type_rk": flow_param_4["data_type"],
            "default_value_json": flow_param_4["value"],
            "version_rk": version_id_4,
            "effective_from_dttm": effective_date_4,
            "effective_to_dttm": LAST_DATE,
            "deleted_flg": False,
        },
        {
            "flow_rk": flow_param_5["flow_id"],
            "flow_parameter_cd": flow_param_5["name"],
            "data_type_rk": flow_param_5["data_type"],
            "default_value_json": flow_param_5["value"],
            "version_rk": version_id_5,
            "effective_from_dttm": effective_date_5,
            "effective_to_dttm": LAST_DATE,
            "deleted_flg": False,
        },
    ]
    assert link_rows == expected_link_rows

    bridge_rows = bridge_flow_parameter_rows(db_session)
    expected_bridge_rows = [
        {
            "flow_parameter_cd": flow_param_1["name"],
            "flow_parameter_name": flow_param_1["name"],
            "data_type_rk": flow_param_1["data_type"],
        },
        {
            "flow_parameter_cd": flow_param_5["name"],
            "flow_parameter_name": flow_param_5["name"],
            "data_type_rk": flow_param_5["data_type"],
        },
    ]

    assert bridge_rows == expected_bridge_rows

    # 6. Удаление потока

    flow_param_6 = {
        "flow_id": 3,
        "flow_name": "flow_test_3",
        "name": "parameter_test_3",
        "data_type": 19,
        "value": None,
    }

    effective_date_6 = datetime(2024, 6, 1, 0, 0, 0, tzinfo=timezone.utc)
    version_id_6 = version_repository.put(
        module_id=module_id,
        major=999,
        minor=999,
        fix=999,
        delivery_date=effective_date_6,
    )
    db_session.commit()

    flow_param_repository.version_id = version_id_6
    flow_param_repository.effective_date = effective_date_6

    with session_resource, flow_param_repository:
        flow_param_repository.load_one(flow_param_6)

    link_rows = link_flow_parameter_rows(db_session)
    expected_link_rows = [
        {
            "flow_rk": flow_param_1["flow_id"],
            "flow_parameter_cd": flow_param_1["name"],
            "data_type_rk": flow_param_1["data_type"],
            "default_value_json": flow_param_1["value"],
            "version_rk": version_id_1,
            "effective_from_dttm": effective_date_1,
            "effective_to_dttm": LAST_DATE,
            "deleted_flg": False,
        },
        {
            "flow_rk": flow_param_2["flow_id"],
            "flow_parameter_cd": flow_param_2["name"],
            "data_type_rk": flow_param_2["data_type"],
            "default_value_json": flow_param_2["value"],
            "version_rk": version_id_4,
            "effective_from_dttm": effective_date_2,
            "effective_to_dttm": effective_date_4,
            "deleted_flg": False,
        },
        {
            "flow_rk": flow_param_4["flow_id"],
            "flow_parameter_cd": flow_param_4["name"],
            "data_type_rk": flow_param_4["data_type"],
            "default_value_json": flow_param_4["value"],
            "version_rk": version_id_4,
            "effective_from_dttm": effective_date_4,
            "effective_to_dttm": effective_date_6,
            "deleted_flg": False,
        },
        {
            "flow_rk": flow_param_4["flow_id"],
            "flow_parameter_cd": flow_param_4["name"],
            "data_type_rk": flow_param_4["data_type"],
            "default_value_json": flow_param_4["value"],
            "version_rk": version_id_4,
            "effective_from_dttm": effective_date_6,
            "effective_to_dttm": LAST_DATE,
            "deleted_flg": True,
        },
        {
            "flow_rk": flow_param_5["flow_id"],
            "flow_parameter_cd": flow_param_5["name"],
            "data_type_rk": flow_param_5["data_type"],
            "default_value_json": flow_param_5["value"],
            "version_rk": version_id_5,
            "effective_from_dttm": effective_date_5,
            "effective_to_dttm": effective_date_6,
            "deleted_flg": False,
        },
        {
            "flow_rk": flow_param_5["flow_id"],
            "flow_parameter_cd": flow_param_5["name"],
            "data_type_rk": flow_param_5["data_type"],
            "default_value_json": flow_param_5["value"],
            "version_rk": version_id_5,
            "effective_from_dttm": effective_date_6,
            "effective_to_dttm": LAST_DATE,
            "deleted_flg": True,
        },
        {
            "flow_rk": flow_param_6["flow_id"],
            "flow_parameter_cd": flow_param_6["name"],
            "data_type_rk": flow_param_6["data_type"],
            "default_value_json": flow_param_6["value"],
            "version_rk": version_id_6,
            "effective_from_dttm": effective_date_6,
            "effective_to_dttm": LAST_DATE,
            "deleted_flg": False,
        },
    ]

    assert link_rows == expected_link_rows

    bridge_rows = bridge_flow_parameter_rows(db_session)
    expected_bridge_rows = [
        {
            "flow_parameter_cd": flow_param_1["name"],
            "flow_parameter_name": flow_param_1["name"],
            "data_type_rk": flow_param_1["data_type"],
        },
        {
            "flow_parameter_cd": flow_param_5["name"],
            "flow_parameter_name": flow_param_5["name"],
            "data_type_rk": flow_param_5["data_type"],
        },
        {
            "flow_parameter_cd": flow_param_6["name"],
            "flow_parameter_name": flow_param_6["name"],
            "data_type_rk": flow_param_6["data_type"],
        },
    ]

    assert bridge_rows == expected_bridge_rows


def link_flow_parameter_rows(db_session: Session):
    return (
        db_session.execute(
            statement=text(f"""
              SELECT l.flow_rk
                   , b.flow_parameter_cd
                   , b.data_type_rk
                   , l.default_value_json
                   , l.version_rk
                   , l.effective_from_dttm
                   , l.effective_to_dttm
                   , l.deleted_flg
                FROM metamodel.link_flow_parameter   l
                JOIN metamodel.bridge_flow_parameter b
                  ON b.flow_parameter_rk = l.flow_parameter_rk
            ORDER BY l.flow_rk
                   , l.flow_parameter_rk
                   , l.effective_from_dttm
            """)
        )
        .mappings()
        .fetchall()
    )


def bridge_flow_parameter_rows(db_session: Session):
    return (
        db_session.execute(
            statement=text("""
              SELECT b.flow_parameter_cd
                   , b.flow_parameter_name
                   , b.data_type_rk
                FROM metamodel.bridge_flow_parameter b
            ORDER BY b.flow_parameter_rk
            """)
        )
        .mappings()
        .fetchall()
    )


@fixture(scope="function")
def version_repository(db_session) -> VersionRepository:
    return VersionRepository(db_session)


@fixture(scope="function")
def truncate_tables(db_session):
    with SessionResource(db_session):
        db_session.execute(text("TRUNCATE metamodel.bridge_version"))
        db_session.execute(text("TRUNCATE metamodel.bridge_flow_parameter"))
        db_session.execute(text("TRUNCATE metamodel.link_flow_parameter"))


@fixture(scope="function")
def insert_dict_code_delivery(db_session):
    with SessionResource(db_session):
        db_session.execute(text("TRUNCATE dict.dict_code_delivery"))
        db_session.execute(
            text("""INSERT INTO dict.dict_code_delivery( code_delivery_rk
                                                       , code_delivery_cd
                                                       , code_delivery_name
                                                       , is_ddl_flg
                                                       , is_flow_flg
                                                       , is_config_flg
                                                       , deleted_flg)
                    VALUES (1, 'core', 'core', FALSE, TRUE, FALSE, FALSE),
                           (2, 'dm', 'dm', FALSE, TRUE, FALSE, FALSE);
            """)
        )
        db_session.commit()
