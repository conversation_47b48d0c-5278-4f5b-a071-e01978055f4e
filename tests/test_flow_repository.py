from datetime import datetime, timezone
from typing import Sequence

from airflow_client.client.model.dag import DAG
from airflow_client.client.model.tag import Tag
from airflow_client.client.model.task import Task
from metaloader_rest_api.common_model import LAST_DATE
from metaloader_rest_api.common_repository import SessionResource
from metaloader_rest_api.flow_model import FlowLinkType, FlowType
from metaloader_rest_api.flow_repository import (
    FlowRepository,
    StageFlow,
    StageFlowRepository,
    StageMasterFlow,
    StageMasterFlowLink,
    StageMasterFlowLinkRepository,
    StageMasterFlowRepository,
)
from metaloader_rest_api.master_files_schemas import (
    ControlFlow as MasterFlow,
)
from metaloader_rest_api.master_files_schemas import (
    FlowGroup,
)
from metaloader_rest_api.master_files_schemas import (
    TriggerFlow as FlowStep,
)
from metaloader_rest_api.master_flow_repository import MasterFlowRepository
from metaloader_rest_api.version_repository import VersionRepository
from pytest import fixture
from sqlalchemy import text
from sqlalchemy.orm import Session


def test_stage_load_flows(
    db_session: Session,
    truncate_flow,
    session_resource: SessionResource,
    table_id: str,
    dags: Sequence[StageFlow],
):
    with session_resource, StageFlowRepository(
        db_session, table_id
    ) as stage_flow_repository:
        stage_flow_repository.load(dags)

    flows = db_session.execute(
        text(f"""
            SELECT *
              FROM {stage_flow_repository.table}
         """)
    ).fetchall()

    assert len(flows) == 2


def test_load_flows(
    db_session: Session,
    truncate_flow,
    session_resource: SessionResource,
    table_id: str,
    core_module_id: int,
    dm_module_id: int,
    core_version_id: int,
    dm_version_id: int,
    dags: Sequence[StageFlow],
    dm_dags: Sequence[StageFlow],
    dags_new: Sequence[StageFlow],
    dags_mf: Sequence[StageFlow],
):
    flow_repository = FlowRepository(db_session)

    with session_resource, StageMasterFlowRepository(
        db_session, table_id
    ) as stage_master_flow_repository:
        pass

    with session_resource, StageFlowRepository(
        db_session, table_id
    ) as stage_flow_repository:
        stage_flow_repository.load(dags)
    with session_resource:
        flow_repository.load_flows(
            core_module_id,
            core_version_id,
            effective_date=datetime(year=2024, month=6, day=15),
            flow_stage_table=stage_flow_repository.table,
            master_flow_stage_table=stage_master_flow_repository.table,
        )

    flows = db_session.execute(
        text("""
            SELECT *
              FROM metamodel.bridge_flow
         """)
    ).fetchall()

    assert len(flows) == 2

    with session_resource, StageFlowRepository(
        db_session, table_id
    ) as stage_flow_repository:
        stage_flow_repository.load(dags)
    with session_resource:
        flow_repository.load_flows(
            core_module_id,
            core_version_id,
            effective_date=datetime(year=2024, month=6, day=21),
            flow_stage_table=stage_flow_repository.table,
            master_flow_stage_table=stage_master_flow_repository.table,
        )

    flows = db_session.execute(
        text("""
            SELECT *
              FROM metamodel.bridge_flow
         """)
    ).fetchall()

    assert len(flows) == 2

    with session_resource, StageFlowRepository(
        db_session, table_id
    ) as stage_flow_repository:
        stage_flow_repository.load(dm_dags)
    with session_resource:
        flow_repository.load_flows(
            dm_module_id,
            dm_version_id,
            effective_date=datetime(year=2024, month=6, day=21),
            flow_stage_table=stage_flow_repository.table,
            master_flow_stage_table=stage_master_flow_repository.table,
        )

    flows = db_session.execute(
        text("""
            SELECT *
              FROM metamodel.bridge_flow
         """)
    ).fetchall()

    assert len(flows) == 4

    with session_resource, StageFlowRepository(
        db_session, table_id
    ) as stage_flow_repository:
        stage_flow_repository.load(dags_new)
    with session_resource:
        flow_repository.load_flows(
            core_module_id,
            core_version_id,
            effective_date=datetime(year=2024, month=6, day=22),
            flow_stage_table=stage_flow_repository.table,
            master_flow_stage_table=stage_master_flow_repository.table,
        )

    flows = db_session.execute(
        text("""
            SELECT *
              FROM metamodel.bridge_flow
         """)
    ).fetchall()

    assert len(flows) == 6

    with session_resource, StageFlowRepository(
        db_session, table_id
    ) as stage_flow_repository:
        pass
    with session_resource:
        flow_repository.load_flows(
            core_module_id,
            core_version_id,
            effective_date=datetime(year=2024, month=6, day=30),
            flow_stage_table=stage_flow_repository.table,
            master_flow_stage_table=stage_master_flow_repository.table,
        )

    flows = db_session.execute(
        text("""
            SELECT *
              FROM metamodel.bridge_flow
         """)
    ).fetchall()

    assert len(flows) == 7

    master_flow_1 = MasterFlow(
        master_flow_name="cf_master_test_1",
        groups=[
            FlowGroup(
                group_id="test_group_1",
                steps=[
                    FlowStep(
                        cf_flow_id="cf_test_1",
                        wrk_flow_id="wf_test_1",
                    ),
                ],
            ),
        ],
    )
    master_flow_2 = MasterFlow(
        master_flow_name="cf_master_test_2",
        groups=[
            FlowGroup(
                group_id="test_group_2",
                steps=[
                    FlowStep(
                        cf_flow_id="cf_test_2",
                        wrk_flow_id="wf_test_2",
                    ),
                ],
            ),
        ],
    )
    with session_resource:
        mf_repository = MasterFlowRepository(session=db_session)
        mf_repository.set_module_id(core_module_id)
        mf_repository.set_version_id(core_version_id)
        mf_repository.set_effective_date(datetime(year=2024, month=7, day=1))
        mf_repository.add_flow(name="cf_master_test_1", flow=master_flow_1)
        mf_repository.add_flow(name="cf_master_test_2", flow=master_flow_2)

    with session_resource, StageFlowRepository(
        db_session, table_id
    ) as stage_flow_repository:
        stage_flow_repository.load(dags_mf)
    with session_resource:
        flow_repository.load_flows(
            core_module_id,
            core_version_id,
            effective_date=datetime(year=2024, month=7, day=2),
            flow_stage_table=stage_flow_repository.table,
            master_flow_stage_table=stage_master_flow_repository.table,
        )

    flows = db_session.execute(
        text("""
            SELECT *
              FROM metamodel.bridge_flow
         """)
    ).fetchall()

    assert len(flows) == 22


def test_load_master_flow_links(
    db_session: Session,
    truncate_flow,
    session_resource: SessionResource,
    table_id: str,
    core_version_id: int,
    core_module_id: int,
    stage_flows: Sequence[StageFlow],
    stage_master_flows: Sequence[StageMasterFlow],
    stage_master_flow_links: Sequence[StageMasterFlowLink],
    new_stage_flows: Sequence[StageFlow],
    new_stage_master_flows: Sequence[StageMasterFlow],
    new_stage_master_flow_links: Sequence[StageMasterFlowLink],
):
    flow_repository = FlowRepository(db_session)

    with session_resource, StageFlowRepository(
        db_session, table_id
    ) as stage_flow_repository, StageMasterFlowRepository(
        db_session, table_id
    ) as stage_master_flow_repository, StageMasterFlowLinkRepository(
        db_session, table_id
    ) as stage_master_flow_link_repository:
        stage_flow_repository.load(stage_flows)
        stage_master_flow_repository.load(stage_master_flows)
        stage_master_flow_link_repository.load(stage_master_flow_links)

    with session_resource:
        flow_repository.load_flows(
            core_module_id,
            core_version_id,
            effective_date=datetime(year=2024, month=6, day=15),
            flow_stage_table=stage_flow_repository.table,
            master_flow_stage_table=stage_master_flow_repository.table,
        )
        flow_repository.load_master_flow_links(
            core_module_id,
            core_version_id,
            effective_date=datetime(year=2024, month=6, day=15),
            master_flow_link_stage_table=stage_master_flow_link_repository.table,
        )

    flows = db_session.execute(
        text("""
            SELECT *
              FROM metamodel.link_flow
         """)
    ).fetchall()

    assert len(flows) == 8

    with session_resource:
        flow_repository.load_flows(
            core_module_id,
            core_version_id,
            effective_date=datetime(year=2024, month=6, day=21),
            flow_stage_table=stage_flow_repository.table,
            master_flow_stage_table=stage_master_flow_repository.table,
        )
        flow_repository.load_master_flow_links(
            core_module_id,
            core_version_id,
            effective_date=datetime(year=2024, month=6, day=21),
            master_flow_link_stage_table=stage_master_flow_link_repository.table,
        )

    flows = db_session.execute(
        text("""
            SELECT *
              FROM metamodel.link_flow
         """)
    ).fetchall()

    assert len(flows) == 8

    with session_resource, StageFlowRepository(
        db_session, table_id
    ) as stage_flow_repository, StageMasterFlowRepository(
        db_session, table_id
    ) as stage_master_flow_repository, StageMasterFlowLinkRepository(
        db_session, table_id
    ) as stage_master_flow_link_repository:
        stage_flow_repository.load(new_stage_flows)
        stage_master_flow_repository.load(new_stage_master_flows)
        stage_master_flow_link_repository.load(new_stage_master_flow_links)

    with session_resource:
        flow_repository.load_flows(
            core_module_id,
            core_version_id,
            effective_date=datetime(year=2024, month=6, day=21),
            flow_stage_table=stage_flow_repository.table,
            master_flow_stage_table=stage_master_flow_repository.table,
        )
        flow_repository.load_master_flow_links(
            core_module_id,
            core_version_id,
            effective_date=datetime(year=2024, month=6, day=21),
            master_flow_link_stage_table=stage_master_flow_link_repository.table,
        )

    flows = db_session.execute(
        text("""
            SELECT *
              FROM metamodel.link_flow
         """)
    ).fetchall()

    assert len(flows) == 12


def test_load_flow_links(
    db_session: Session,
    truncate_flow,
    session_resource: SessionResource,
    table_id: str,
    core_version_id: int,
    core_module_id: int,
    link_dags: Sequence[StageFlow],
    link_dags_new: Sequence[StageFlow],
):
    flow_repository = FlowRepository(db_session)

    with session_resource, StageMasterFlowRepository(
        db_session, table_id
    ) as stage_master_flow_repository:
        pass

    with session_resource, StageFlowRepository(
        db_session, table_id
    ) as stage_flow_repository:
        stage_flow_repository.load(link_dags)
    with session_resource:
        flow_repository.load_flows(
            core_module_id,
            core_version_id,
            effective_date=datetime(year=2024, month=6, day=15),
            flow_stage_table=stage_flow_repository.table,
            master_flow_stage_table=stage_master_flow_repository.table,
        )
        flow_repository.load_flow_links(
            core_module_id,
            core_version_id,
            effective_date=datetime(year=2024, month=6, day=15),
        )

    flows = db_session.execute(
        text("""
            SELECT *
              FROM metamodel.link_flow
         """)
    ).fetchall()

    assert len(flows) == 5

    with session_resource, StageFlowRepository(
        db_session, table_id
    ) as stage_flow_repository:
        stage_flow_repository.load(link_dags)
    with session_resource:
        flow_repository.load_flows(
            core_module_id,
            core_version_id,
            effective_date=datetime(year=2024, month=6, day=21),
            flow_stage_table=stage_flow_repository.table,
            master_flow_stage_table=stage_master_flow_repository.table,
        )
    with session_resource:
        flow_repository.load_flow_links(
            core_module_id,
            core_version_id,
            effective_date=datetime(year=2024, month=6, day=21),
        )

    flows = db_session.execute(
        text("""
            SELECT *
              FROM metamodel.link_flow
         """)
    ).fetchall()

    assert len(flows) == 5

    with session_resource, StageFlowRepository(
        db_session, table_id
    ) as stage_flow_repository:
        stage_flow_repository.load(link_dags_new)
    with session_resource:
        flow_repository.load_flows(
            core_module_id,
            core_version_id,
            effective_date=datetime(year=2024, month=6, day=30),
            flow_stage_table=stage_flow_repository.table,
            master_flow_stage_table=stage_master_flow_repository.table,
        )
        flow_repository.load_flow_links(
            core_module_id,
            core_version_id,
            effective_date=datetime(year=2024, month=6, day=30),
        )

    flows = db_session.execute(
        text("""
            SELECT *
              FROM metamodel.link_flow
         """)
    ).fetchall()

    assert len(flows) == 9


def test_load_flow_links_with_tags_and_name(
    db_session: Session,
    truncate_flow,
    session_resource: SessionResource,
    table_id: str,
    core_version_id: int,
    core_module_id: int,
):
    link_dags: Sequence[StageFlow] = [
        StageFlow(
            name="cf_test_dag_1",
            type=FlowType.CONTROL,
            description=None,
            tags=None,
            dag=None,
            tasks=None,
        ),
        StageFlow(
            name="cf_test_dag_2",
            type=FlowType.CONTROL,
            description=None,
            tags=None,
            dag=None,
            tasks=None,
        ),
        StageFlow(
            name="wf_test_dag_1",
            type=FlowType.WORK,
            description=None,
            tags=["cf: cf_test_dag_2"],
            dag=None,
            tasks=None,
        ),
        StageFlow(
            name="wf_test_dag_2",
            type=FlowType.WORK,
            description=None,
            tags=["cf: cf_test_dag_2"],
            dag=None,
            tasks=None,
        ),
    ]

    effective_date = datetime(2024, 6, 15, 0, 0, 0, tzinfo=timezone.utc)
    flow_repository = FlowRepository(db_session)

    with session_resource, StageMasterFlowRepository(
        db_session, table_id
    ) as stage_master_flow_repository:
        pass

    with session_resource, StageFlowRepository(
        db_session, table_id
    ) as stage_flow_repository:
        stage_flow_repository.load(link_dags)
    with session_resource:
        flow_repository.load_flows(
            core_module_id,
            core_version_id,
            effective_date=effective_date,
            flow_stage_table=stage_flow_repository.table,
            master_flow_stage_table=stage_master_flow_repository.table,
        )
        flow_repository.load_flow_links(
            core_module_id,
            core_version_id,
            effective_date=effective_date,
        )

    bridge_rows = (
        db_session.execute(
            statement=text(f"""
            SELECT flow_name
                 , flow_type_rk
                 , tag_list
                 , version_rk
                 , effective_from_dttm
                 , effective_to_dttm
                 , deleted_flg
              FROM metamodel.bridge_flow;
             """)
        )
        .mappings()
        .fetchall()
    )
    expected_bridge_rows = [
        {
            "flow_name": "cf_test_dag_1",
            "flow_type_rk": FlowType.CONTROL.value,
            "tag_list": None,
            "version_rk": core_version_id,
            "effective_from_dttm": effective_date,
            "effective_to_dttm": LAST_DATE,
            "deleted_flg": False,
        },
        {
            "flow_name": "cf_test_dag_2",
            "flow_type_rk": FlowType.CONTROL.value,
            "tag_list": None,
            "version_rk": core_version_id,
            "effective_from_dttm": effective_date,
            "effective_to_dttm": LAST_DATE,
            "deleted_flg": False,
        },
        {
            "flow_name": "wf_test_dag_1",
            "flow_type_rk": FlowType.WORK.value,
            "tag_list": ["cf: cf_test_dag_2"],
            "version_rk": core_version_id,
            "effective_from_dttm": effective_date,
            "effective_to_dttm": LAST_DATE,
            "deleted_flg": False,
        },
        {
            "flow_name": "wf_test_dag_2",
            "flow_type_rk": FlowType.WORK.value,
            "tag_list": ["cf: cf_test_dag_2"],
            "version_rk": core_version_id,
            "effective_from_dttm": effective_date,
            "effective_to_dttm": LAST_DATE,
            "deleted_flg": False,
        },
    ]

    assert bridge_rows == expected_bridge_rows

    link_rows = (
        db_session.execute(
            statement=text(f"""
               SELECT l.owner_flow_rk
                    , s.flow_name source_flow_name
                    , t.flow_name target_flow_name
                    , l.flow_link_type_rk
                    , l.version_rk
                    , l.effective_from_dttm
                    , l.effective_to_dttm
                    , l.deleted_flg
                 FROM metamodel.link_flow l
            LEFT JOIN metamodel.bridge_flow s
                   ON s.flow_rk = l.source_flow_rk
            LEFT JOIN metamodel.bridge_flow t
                   ON t.flow_rk = l.target_flow_rk;
             """)
        )
        .mappings()
        .fetchall()
    )
    expected_link_rows = [
        {
            "owner_flow_rk": -1,
            "source_flow_name": "cf_test_dag_1",
            "target_flow_name": "wf_test_dag_1",
            "flow_link_type_rk": FlowLinkType.CONTROL.value,
            "version_rk": core_version_id,
            "effective_from_dttm": effective_date,
            "effective_to_dttm": LAST_DATE,
            "deleted_flg": False,
        },
        {
            "owner_flow_rk": -1,
            "source_flow_name": "cf_test_dag_2",
            "target_flow_name": "wf_test_dag_1",
            "flow_link_type_rk": FlowLinkType.CONTROL.value,
            "version_rk": core_version_id,
            "effective_from_dttm": effective_date,
            "effective_to_dttm": LAST_DATE,
            "deleted_flg": False,
        },
        {
            "owner_flow_rk": -1,
            "source_flow_name": "cf_test_dag_2",
            "target_flow_name": "wf_test_dag_2",
            "flow_link_type_rk": FlowLinkType.CONTROL.value,
            "version_rk": core_version_id,
            "effective_from_dttm": effective_date,
            "effective_to_dttm": LAST_DATE,
            "deleted_flg": False,
        },
    ]

    assert link_rows == expected_link_rows


def test_load_flow_links_with_tags_and_name_and_master_flow(
    db_session: Session,
    truncate_flow,
    session_resource: SessionResource,
    table_id: str,
    core_version_id: int,
    core_module_id: int,
):
    stage_master_flows = [StageMasterFlow(name="cf_master_test:group_test_1")]

    stage_master_flow_links = [
        StageMasterFlowLink(
            owner_flow_name="cf_master_test",
            source_flow_name="cf_master_test",
            target_flow_name="cf_master_test:group_test_1",
        ),
        StageMasterFlowLink(
            owner_flow_name="cf_master_test:group_test_1",
            source_flow_name="cf_master_test:group_test_1",
            target_flow_name="cf_test_dag_1",
        ),
        StageMasterFlowLink(
            owner_flow_name="cf_master_test:group_test_1",
            source_flow_name="cf_test_dag_1",
            target_flow_name=None,
        ),
    ]

    link_dags: Sequence[StageFlow] = [
        StageFlow(
            name="cf_master_test",
            type=FlowType.MASTER,
            description=None,
            tags=None,
            dag=None,
            tasks=None,
        ),
        StageFlow(
            name="cf_test_dag_1",
            type=FlowType.CONTROL,
            description=None,
            tags=None,
            dag=None,
            tasks=None,
        ),
        StageFlow(
            name="wf_test_dag_1",
            type=FlowType.WORK,
            description=None,
            tags=["cf: cf_test_dag_1"],
            dag=None,
            tasks=None,
        ),
        StageFlow(
            name="wf_test_dag_2",
            type=FlowType.WORK,
            description=None,
            tags=["cf: cf_test_dag_1"],
            dag=None,
            tasks=None,
        ),
    ]

    effective_date = datetime(2024, 6, 15, 0, 0, 0, tzinfo=timezone.utc)
    flow_repository = FlowRepository(db_session)

    with session_resource, StageFlowRepository(
        db_session, table_id
    ) as stage_flow_repository, StageMasterFlowRepository(
        db_session, table_id
    ) as stage_master_flow_repository, StageMasterFlowLinkRepository(
        db_session, table_id
    ) as stage_master_flow_link_repository:
        stage_flow_repository.load(link_dags)
        stage_master_flow_repository.load(stage_master_flows)
        stage_master_flow_link_repository.load(stage_master_flow_links)

    with session_resource:
        flow_repository.load_flows(
            core_module_id,
            core_version_id,
            effective_date=effective_date,
            flow_stage_table=stage_flow_repository.table,
            master_flow_stage_table=stage_master_flow_repository.table,
        )
        flow_repository.load_master_flow_links(
            core_module_id,
            core_version_id,
            effective_date=datetime(year=2024, month=6, day=15),
            master_flow_link_stage_table=stage_master_flow_link_repository.table,
        )
        flow_repository.load_flow_links(
            core_module_id,
            core_version_id,
            effective_date=effective_date,
        )

    link_rows = (
        db_session.execute(
            statement=text(f"""
                SELECT of.flow_name owner_flow_name
                     , sf.flow_name source_flow_name
                     , tf.flow_name target_flow_name
                     , l.flow_link_type_rk
                     , l.effective_from_dttm
                     , l.effective_to_dttm
                     , l.version_rk
                     , l.deleted_flg
                  FROM metamodel.link_flow l
             LEFT JOIN metamodel.bridge_flow of
                    ON of.flow_rk = l.owner_flow_rk
             LEFT JOIN metamodel.bridge_flow sf
                    ON sf.flow_rk = l.source_flow_rk
             LEFT JOIN metamodel.bridge_flow tf
                    ON tf.flow_rk = l.target_flow_rk
              ORDER BY owner_flow_name NULLS LAST, source_flow_name;
             """)
        )
        .mappings()
        .fetchall()
    )

    expected_link_rows = [
        {
            "owner_flow_name": "cf_master_test",
            "source_flow_name": "cf_master_test",
            "target_flow_name": "cf_master_test:group_test_1",
            "flow_link_type_rk": FlowLinkType.SUBSCRIBER.value,
            "effective_from_dttm": effective_date,
            "effective_to_dttm": LAST_DATE,
            "version_rk": core_version_id,
            "deleted_flg": False,
        },
        {
            "owner_flow_name": "cf_master_test:group_test_1",
            "source_flow_name": "cf_master_test:group_test_1",
            "target_flow_name": "cf_test_dag_1",
            "flow_link_type_rk": FlowLinkType.SUBSCRIBER.value,
            "effective_from_dttm": effective_date,
            "effective_to_dttm": LAST_DATE,
            "version_rk": core_version_id,
            "deleted_flg": False,
        },
        {
            "owner_flow_name": "cf_master_test:group_test_1",
            "source_flow_name": "cf_test_dag_1",
            "target_flow_name": "wf_test_dag_1",
            "flow_link_type_rk": FlowLinkType.CONTROL.value,
            "effective_from_dttm": effective_date,
            "effective_to_dttm": LAST_DATE,
            "version_rk": core_version_id,
            "deleted_flg": False,
        },
        {
            "owner_flow_name": None,
            "source_flow_name": "cf_test_dag_1",
            "target_flow_name": "wf_test_dag_2",
            "flow_link_type_rk": FlowLinkType.CONTROL.value,
            "effective_from_dttm": effective_date,
            "effective_to_dttm": LAST_DATE,
            "version_rk": core_version_id,
            "deleted_flg": False,
        },
    ]

    assert link_rows == expected_link_rows


def test_delete_and_restore_flows(
    db_session: Session,
    truncate_flow,
    session_resource: SessionResource,
    table_id,
    core_module_id,
    core_version_id,
):
    link_dags: Sequence[StageFlow] = [
        StageFlow(
            name="cf_master_test",
            type=FlowType.MASTER,
            description=None,
            tags=None,
            dag=None,
            tasks=None,
        ),
        StageFlow(
            name="wf_test_dag_1",
            type=FlowType.WORK,
            description=None,
            tags=None,
            dag=None,
            tasks=None,
        ),
    ]

    effective_date = datetime(2024, 6, 15, 0, 0, 0, tzinfo=timezone.utc)
    flow_repository = FlowRepository(db_session)

    with session_resource, StageMasterFlowRepository(
        db_session, table_id
    ) as stage_master_flow_repository:
        pass
    with session_resource, StageFlowRepository(
        db_session, table_id
    ) as stage_flow_repository:
        stage_flow_repository.load(link_dags)
    with session_resource:
        flow_repository.load_flows(
            core_module_id,
            core_version_id,
            effective_date=effective_date,
            flow_stage_table=stage_flow_repository.table,
            master_flow_stage_table=stage_master_flow_repository.table,
        )
        flow_repository.load_flow_links(
            core_module_id,
            core_version_id,
            effective_date=effective_date,
        )

    link_dags: Sequence[StageFlow] = []

    effective_date = datetime(2024, 8, 15, 0, 0, 0, tzinfo=timezone.utc)
    flow_repository = FlowRepository(db_session)

    with session_resource, StageMasterFlowRepository(
        db_session, table_id
    ) as stage_master_flow_repository:
        pass

    with session_resource, StageFlowRepository(
        db_session, table_id
    ) as stage_flow_repository:
        stage_flow_repository.load(link_dags)
    with session_resource:
        flow_repository.load_flows(
            core_module_id,
            core_version_id,
            effective_date=effective_date,
            flow_stage_table=stage_flow_repository.table,
            master_flow_stage_table=stage_master_flow_repository.table,
        )
        flow_repository.load_flow_links(
            core_module_id,
            core_version_id,
            effective_date=effective_date,
        )

    link_dags: Sequence[StageFlow] = [
        StageFlow(
            name="cf_master_test",
            type=FlowType.MASTER,
            description=None,
            tags=None,
            dag=None,
            tasks=None,
        ),
        StageFlow(
            name="wf_test_dag_1",
            type=FlowType.WORK,
            description=None,
            tags=None,
            dag=None,
            tasks=None,
        ),
    ]

    effective_date = datetime(2024, 11, 15, 0, 0, 0, tzinfo=timezone.utc)
    flow_repository = FlowRepository(db_session)

    with session_resource, StageMasterFlowRepository(
        db_session, table_id
    ) as stage_master_flow_repository:
        pass

    with session_resource, StageFlowRepository(
        db_session, table_id
    ) as stage_flow_repository:
        stage_flow_repository.load(link_dags)
    with session_resource:
        flow_repository.load_flows(
            core_module_id,
            core_version_id,
            effective_date=effective_date,
            flow_stage_table=stage_flow_repository.table,
            master_flow_stage_table=stage_master_flow_repository.table,
        )
        flow_repository.load_flow_links(
            core_module_id,
            core_version_id,
            effective_date=effective_date,
        )

    flows = db_session.execute(
        text("""
            SELECT code_delivery_rk, flow_rk, flow_type_rk, flow_name, effective_from_dttm, effective_to_dttm, deleted_flg
              FROM metamodel.bridge_flow
          ORDER BY flow_rk, effective_from_dttm;
          """)
    ).fetchall()

    assert len(flows) == 6


def test_delete_and_restore_master_flow_links(
    db_session: Session,
    truncate_flow,
    session_resource: SessionResource,
    table_id: str,
    core_version_id: int,
    core_module_id: int,
):
    stage_master_flows = [StageMasterFlow(name="cf_master_test:group_test_1")]

    stage_master_flow_links = [
        StageMasterFlowLink(
            owner_flow_name="cf_master_test",
            source_flow_name="cf_master_test",
            target_flow_name="cf_master_test:group_test_1",
        ),
        StageMasterFlowLink(
            owner_flow_name="cf_master_test:group_test_1",
            source_flow_name="cf_master_test:group_test_1",
            target_flow_name="cf_test_dag_1",
        ),
        StageMasterFlowLink(
            owner_flow_name="cf_master_test:group_test_1",
            source_flow_name="cf_test_dag_1",
            target_flow_name=None,
        ),
    ]

    link_dags: Sequence[StageFlow] = [
        StageFlow(
            name="cf_master_test",
            type=FlowType.MASTER,
            description=None,
            tags=None,
            dag=None,
            tasks=None,
        ),
        StageFlow(
            name="cf_test_dag_1",
            type=FlowType.CONTROL,
            description=None,
            tags=None,
            dag=None,
            tasks=None,
        ),
        StageFlow(
            name="wf_test_dag_1",
            type=FlowType.WORK,
            description=None,
            tags=None,
            dag=None,
            tasks=None,
        ),
        StageFlow(
            name="cf_test_dag_3",
            type=FlowType.CONTROL,
            description=None,
            tags=None,
            dag=None,
            tasks=None,
        ),
        StageFlow(
            name="wf_test_dag_2",
            type=FlowType.WORK,
            description=None,
            tags=None,
            dag=None,
            tasks=None,
        ),
    ]

    effective_date = datetime(2024, 6, 15, 0, 0, 0, tzinfo=timezone.utc)
    flow_repository = FlowRepository(db_session)

    with session_resource, StageFlowRepository(
        db_session, table_id
    ) as stage_flow_repository, StageMasterFlowRepository(
        db_session, table_id
    ) as stage_master_flow_repository, StageMasterFlowLinkRepository(
        db_session, table_id
    ) as stage_master_flow_link_repository:
        stage_flow_repository.load(link_dags)
        stage_master_flow_repository.load(stage_master_flows)
        stage_master_flow_link_repository.load(stage_master_flow_links)

    with session_resource:
        flow_repository.load_flows(
            core_module_id,
            core_version_id,
            effective_date=effective_date,
            flow_stage_table=stage_flow_repository.table,
            master_flow_stage_table=stage_master_flow_repository.table,
        )
        flow_repository.load_master_flow_links(
            core_module_id,
            core_version_id,
            effective_date=effective_date,
            master_flow_link_stage_table=stage_master_flow_link_repository.table,
        )
        flow_repository.load_flow_links(
            core_module_id,
            core_version_id,
            effective_date=effective_date,
        )

    stage_master_flows = [StageMasterFlow(name="cf_master_test:group_test_1")]

    stage_master_flow_links = [
        StageMasterFlowLink(
            owner_flow_name="cf_master_test",
            source_flow_name="cf_master_test",
            target_flow_name="cf_master_test:group_test_1",
        ),
        StageMasterFlowLink(
            owner_flow_name="cf_master_test:group_test_1",
            source_flow_name="cf_master_test:group_test_1",
            target_flow_name="cf_test_dag_3",
        ),
        StageMasterFlowLink(
            owner_flow_name="cf_master_test:group_test_1",
            source_flow_name="cf_test_dag_3",
            target_flow_name=None,
        ),
    ]

    effective_date = datetime(2024, 9, 22, 0, 0, 0, tzinfo=timezone.utc)

    with session_resource, StageFlowRepository(
        db_session, table_id
    ) as stage_flow_repository, StageMasterFlowRepository(
        db_session, table_id
    ) as stage_master_flow_repository, StageMasterFlowLinkRepository(
        db_session, table_id
    ) as stage_master_flow_link_repository:
        stage_flow_repository.load(link_dags)
        stage_master_flow_repository.load(stage_master_flows)
        stage_master_flow_link_repository.load(stage_master_flow_links)

    with session_resource:
        flow_repository.load_flows(
            core_module_id,
            core_version_id,
            effective_date=effective_date,
            flow_stage_table=stage_flow_repository.table,
            master_flow_stage_table=stage_master_flow_repository.table,
        )
        flow_repository.load_master_flow_links(
            core_module_id,
            core_version_id,
            effective_date=effective_date,
            master_flow_link_stage_table=stage_master_flow_link_repository.table,
        )
        flow_repository.load_flow_links(
            core_module_id,
            core_version_id,
            effective_date=effective_date,
        )

    stage_master_flows = [StageMasterFlow(name="cf_master_test:group_test_1")]

    stage_master_flow_links = [
        StageMasterFlowLink(
            owner_flow_name="cf_master_test",
            source_flow_name="cf_master_test",
            target_flow_name="cf_master_test:group_test_1",
        ),
        StageMasterFlowLink(
            owner_flow_name="cf_master_test:group_test_1",
            source_flow_name="cf_master_test:group_test_1",
            target_flow_name="cf_test_dag_1",
        ),
        StageMasterFlowLink(
            owner_flow_name="cf_master_test:group_test_1",
            source_flow_name="cf_test_dag_1",
            target_flow_name=None,
        ),
    ]

    effective_date = datetime(2024, 11, 15, 0, 0, 0, tzinfo=timezone.utc)
    flow_repository = FlowRepository(db_session)

    with session_resource, StageFlowRepository(
        db_session, table_id
    ) as stage_flow_repository, StageMasterFlowRepository(
        db_session, table_id
    ) as stage_master_flow_repository, StageMasterFlowLinkRepository(
        db_session, table_id
    ) as stage_master_flow_link_repository:
        stage_flow_repository.load(link_dags)
        stage_master_flow_repository.load(stage_master_flows)
        stage_master_flow_link_repository.load(stage_master_flow_links)

    with session_resource:
        flow_repository.load_flows(
            core_module_id,
            core_version_id,
            effective_date=effective_date,
            flow_stage_table=stage_flow_repository.table,
            master_flow_stage_table=stage_master_flow_repository.table,
        )
        flow_repository.load_master_flow_links(
            core_module_id,
            core_version_id,
            effective_date=effective_date,
            master_flow_link_stage_table=stage_master_flow_link_repository.table,
        )
        flow_repository.load_flow_links(
            core_module_id,
            core_version_id,
            effective_date=effective_date,
        )

    link_flows = db_session.execute(
        text("""
            SELECT of.flow_name owner_flow_name
                 , sf.flow_name source_flow_name
                 , tf.flow_name target_flow_name
                 , l.flow_link_type_rk
                 , l.effective_from_dttm
                 , l.effective_to_dttm
                 , l.version_rk
                 , l.deleted_flg
              FROM metamodel.link_flow l
         LEFT JOIN metamodel.bridge_flow of
                ON of.flow_rk = l.owner_flow_rk
         LEFT JOIN metamodel.bridge_flow sf
                ON sf.flow_rk = l.source_flow_rk
         LEFT JOIN metamodel.bridge_flow tf
                ON tf.flow_rk = l.target_flow_rk
          ORDER BY owner_flow_name NULLS LAST
                 , source_flow_name
                 , target_flow_rk
                 , effective_from_dttm;
          """)
    ).fetchall()

    assert len(link_flows) == 12


def test_delete_and_restore_flow_links(
    db_session: Session,
    truncate_flow,
    session_resource: SessionResource,
    table_id: str,
    core_version_id: int,
    core_module_id: int,
):
    effective_date = datetime(2024, 6, 15, tzinfo=timezone.utc)
    flow_repository = FlowRepository(db_session)

    link_dags: Sequence[StageFlow] = [
        StageFlow(
            name="cf_test_dag_1",
            type=FlowType.WORK,
            description=None,
            tags=None,
            dag=None,
            tasks=None,
        ),
        StageFlow(
            name="wf_test_dag_1",
            type=FlowType.WORK,
            description=None,
            tags=["cf: cf_test_dag_1"],
            dag=None,
            tasks=None,
        ),
    ]

    with session_resource, StageMasterFlowRepository(
        db_session, table_id
    ) as stage_master_flow_repository:
        pass
    with session_resource, StageFlowRepository(
        db_session, table_id
    ) as stage_flow_repository:
        stage_flow_repository.load(link_dags)
    with session_resource:
        flow_repository.load_flows(
            core_module_id,
            core_version_id,
            effective_date=effective_date,
            flow_stage_table=stage_flow_repository.table,
            master_flow_stage_table=stage_master_flow_repository.table,
        )
        flow_repository.load_flow_links(
            core_module_id,
            core_version_id,
            effective_date=effective_date,
        )

    effective_date = datetime(2024, 7, 15, tzinfo=timezone.utc)

    with session_resource, StageMasterFlowRepository(
        db_session, table_id
    ) as stage_master_flow_repository:
        pass
    with session_resource, StageFlowRepository(
        db_session, table_id
    ) as stage_flow_repository:
        stage_flow_repository.load([])
    with session_resource:
        flow_repository.load_flows(
            core_module_id,
            core_version_id,
            effective_date=effective_date,
            flow_stage_table=stage_flow_repository.table,
            master_flow_stage_table=stage_master_flow_repository.table,
        )
        flow_repository.load_flow_links(
            core_module_id,
            core_version_id,
            effective_date=effective_date,
        )

    effective_date = datetime(2024, 8, 15, tzinfo=timezone.utc)

    with session_resource, StageMasterFlowRepository(
        db_session, table_id
    ) as stage_master_flow_repository:
        pass
    with session_resource, StageFlowRepository(
        db_session, table_id
    ) as stage_flow_repository:
        stage_flow_repository.load(link_dags)
    with session_resource:
        flow_repository.load_flows(
            core_module_id,
            core_version_id,
            effective_date=effective_date,
            flow_stage_table=stage_flow_repository.table,
            master_flow_stage_table=stage_master_flow_repository.table,
        )
        flow_repository.load_flow_links(
            core_module_id,
            core_version_id,
            effective_date=effective_date,
        )

    link_flows = db_session.execute(
        text("""SELECT * FROM metamodel.link_flow;""")
    ).fetchall()

    assert len(link_flows) == 3


@fixture(scope="function")
def core_module_id(db_session) -> int:
    add_module_id(db_session, "core", 1)

    return 1


@fixture(scope="function")
def dm_module_id(db_session) -> int:
    add_module_id(db_session, "dm", 2)

    return 2


def add_module_id(
    db_session,
    name: str,
    id: int,
) -> None:
    db_session.execute(
        statement=text("""
            INSERT INTO dict.dict_code_delivery(code_delivery_rk
                                              , code_delivery_cd
                                              , code_delivery_name
                                              , is_ddl_flg
                                              , is_flow_flg
                                              , is_config_flg
                                              , deleted_flg)
            VALUES (:code_delivery_rk
                  , :code_delivery_name
                  , :code_delivery_name
                  , FALSE
                  , TRUE
                  , FALSE
                  , FALSE)
        """),
        params={
            "code_delivery_rk": id,
            "code_delivery_name": name,
        },
    )
    db_session.commit()


@fixture(scope="function")
def core_version_id(
    db_session,
    session_resource,
    core_module_id,
) -> int:
    return add_version_id(db_session, session_resource, core_module_id)


@fixture(scope="function")
def dm_version_id(
    db_session,
    session_resource,
    dm_module_id,
) -> int:
    return add_version_id(db_session, session_resource, dm_module_id)


def add_version_id(
    db_session,
    session_resource,
    module_id: int,
) -> int:
    with session_resource:
        version_repository = VersionRepository(db_session)
        return version_repository.put(
            module_id,
            major=1,
            minor=2,
            fix=3,
        )


@fixture(scope="module")
def dags() -> Sequence[StageFlow]:
    return [
        StageFlow(
            name="cf_test_dag",
            type=FlowType.CONTROL,
            description="Test DAG",
            tags=["test_tag_1", "test_tag_2"],
            dag=DAG._from_openapi_data(
                dag_id="cf_test_dag",
                description="Test DAG",
                tags=[
                    Tag._from_openapi_data(name="test_tag_1"),
                    Tag._from_openapi_data(name="test_tag_2"),
                ],
            ).to_dict(),
            tasks=[
                Task._from_openapi_data(
                    task_id="test_task_1",
                ).to_dict(),
                Task._from_openapi_data(
                    task_id="test_task_2",
                ).to_dict(),
            ],
        ),
        StageFlow(
            name="cf_test_another_dag",
            type=FlowType.WORK,
            description="Test Another DAG",
            tags=[],
            dag=DAG._from_openapi_data(
                dag_id="cf_test_another_dag",
                description="Test Another DAG",
                tags=[],
            ).to_dict(),
            tasks=[
                Task._from_openapi_data(
                    task_id="test_task_1",
                ).to_dict(),
            ],
        ),
    ]


@fixture(scope="module")
def dm_dags() -> Sequence[StageFlow]:
    return [
        StageFlow(
            name="cf_test_dag",
            type=FlowType.CONTROL,
            description="Test DAG",
            tags=["test_tag_1", "test_tag_2"],
            dag=DAG._from_openapi_data(
                dag_id="cf_test_dag",
                description="Test DAG",
                tags=[
                    Tag._from_openapi_data(name="test_tag_1"),
                    Tag._from_openapi_data(name="test_tag_2"),
                ],
            ).to_dict(),
            tasks=[
                Task._from_openapi_data(
                    task_id="test_task_1",
                ).to_dict(),
                Task._from_openapi_data(
                    task_id="test_task_2",
                ).to_dict(),
            ],
        ),
        StageFlow(
            name="cf_test_yet_another_dag",
            type=FlowType.WORK,
            description="Test Yet Another DAG",
            tags=[],
            dag=DAG._from_openapi_data(
                dag_id="cf_test_yet_another_dag",
                description="Test Yet Another DAG",
                tags=[],
            ).to_dict(),
            tasks=[
                Task._from_openapi_data(
                    task_id="test_task_1",
                ).to_dict(),
            ],
        ),
    ]


@fixture(scope="module")
def dags_new() -> Sequence[StageFlow]:
    return [
        StageFlow(
            name="cf_test_dag",
            type=FlowType.CONTROL,
            description="Test DAG",
            tags=["test_tag_1", "test_tag_2", "test_tag_3"],
            dag=DAG._from_openapi_data(
                dag_id="cf_test_dag",
                description="Test DAG",
                tags=[
                    Tag._from_openapi_data(name="test_tag_1"),
                    Tag._from_openapi_data(name="test_tag_2"),
                    Tag._from_openapi_data(name="test_tag_3"),
                ],
            ).to_dict(),
            tasks=[
                Task._from_openapi_data(
                    task_id="test_task_1",
                ).to_dict(),
                Task._from_openapi_data(
                    task_id="test_task_2",
                ).to_dict(),
            ],
        )
    ]


@fixture(scope="module")
def dags_mf() -> Sequence[StageFlow]:
    return [
        StageFlow(
            name="cf_test",
            type=FlowType.CONTROL,
            description=None,
            tags=None,
            dag=None,
            tasks=None,
        ),
        StageFlow(
            name="wf_test",
            type=FlowType.WORK,
            description=None,
            tags=None,
            dag=None,
            tasks=None,
        ),
        StageFlow(
            name="cf_master_test_1",
            type=FlowType.MASTER,
            description=None,
            tags=None,
            dag=None,
            tasks=None,
        ),
        StageFlow(
            name="cf_test_1",
            type=FlowType.CONTROL,
            description=None,
            tags=None,
            dag=None,
            tasks=None,
        ),
        StageFlow(
            name="wf_test_1",
            type=FlowType.WORK,
            description=None,
            tags=None,
            dag=None,
            tasks=None,
        ),
    ]


@fixture(scope="module")
def link_dags() -> Sequence[StageFlow]:
    return [
        StageFlow(
            name="cf_test_dag_0_c",
            type=FlowType.CONTROL,
            description=None,
            tags=None,
            dag=None,
            tasks=None,
        ),
        StageFlow(
            name="wf_test_dag_0_w",
            type=FlowType.WORK,
            description=None,
            tags=None,
            dag=None,
            tasks=None,
        ),
        StageFlow(
            name="cf_test_dag_1",
            type=FlowType.CONTROL,
            description=None,
            tags=None,
            dag=None,
            tasks=None,
        ),
        StageFlow(
            name="wf_test_dag_1",
            type=FlowType.WORK,
            description=None,
            tags=None,
            dag=None,
            tasks=None,
        ),
        StageFlow(
            name="cf_test_dag_2",
            type=FlowType.CONTROL,
            description=None,
            tags=None,
            dag=None,
            tasks=None,
        ),
        StageFlow(
            name="wrk_test_dag_2",
            type=FlowType.WORK,
            description=None,
            tags=None,
            dag=None,
            tasks=None,
        ),
        StageFlow(
            name="cf_test_dag_3_0",
            type=FlowType.CONTROL,
            description=None,
            tags=["wrk: wrk_test_dag_3"],
            dag=None,
            tasks=None,
        ),
        StageFlow(
            name="wrk_test_dag_3",
            type=FlowType.WORK,
            description=None,
            tags=None,
            dag=None,
            tasks=None,
        ),
        StageFlow(
            name="cf_test_dag_4",
            type=FlowType.CONTROL,
            description=None,
            tags=None,
            dag=None,
            tasks=None,
        ),
        StageFlow(
            name="wrk_test_dag_4_0",
            type=FlowType.WORK,
            description=None,
            tags=["cf: cf_test_dag_4"],
            dag=None,
            tasks=None,
        ),
        StageFlow(
            name="cf_test_dag_5_w",
            type=FlowType.CONTROL,
            description=None,
            tags=["wrk: wrk_test_dag_5_c"],
            dag=None,
            tasks=None,
        ),
        StageFlow(
            name="wrk_test_dag_5_c",
            type=FlowType.WORK,
            description=None,
            tags=["cf: cf_test_dag_5_w"],
            dag=None,
            tasks=None,
        ),
    ]


@fixture(scope="module")
def link_dags_new() -> Sequence[StageFlow]:
    return [
        StageFlow(
            name="cf_test_dag_0_c",
            type=FlowType.CONTROL,
            description=None,
            tags=None,
            dag=None,
            tasks=None,
        ),
        StageFlow(
            name="wf_test_dag_0_w",
            type=FlowType.WORK,
            description=None,
            tags=None,
            dag=None,
            tasks=None,
        ),
        StageFlow(
            name="wf_test_dag_1",
            type=FlowType.WORK,
            description=None,
            tags=None,
            dag=None,
            tasks=None,
        ),
        StageFlow(
            name="cf_test_dag_2",
            type=FlowType.CONTROL,
            description=None,
            tags=None,
            dag=None,
            tasks=None,
        ),
        StageFlow(
            name="cf_test_dag_3_0",
            type=FlowType.CONTROL,
            description=None,
            tags=["wrk: wrk_test_dag_3"],
            dag=None,
            tasks=None,
        ),
        StageFlow(
            name="wrk_test_dag_3",
            type=FlowType.WORK,
            description=None,
            tags=None,
            dag=None,
            tasks=None,
        ),
        StageFlow(
            name="cf_test_dag_4",
            type=FlowType.CONTROL,
            description=None,
            tags=None,
            dag=None,
            tasks=None,
        ),
        StageFlow(
            name="wrk_test_dag_4_0",
            type=FlowType.WORK,
            description=None,
            tags=None,
            dag=None,
            tasks=None,
        ),
        StageFlow(
            name="cf_test_dag_5_w",
            type=FlowType.CONTROL,
            description=None,
            tags=["wrk: wrk_test_dag_5_c"],
            dag=None,
            tasks=None,
        ),
        StageFlow(
            name="wrk_test_dag_5_c",
            type=FlowType.WORK,
            description=None,
            tags=None,
            dag=None,
            tasks=None,
        ),
        StageFlow(
            name="cf_test_dag_6",
            type=FlowType.CONTROL,
            description=None,
            tags=None,
            dag=None,
            tasks=None,
        ),
        StageFlow(
            name="wrk_test_dag_6",
            type=FlowType.WORK,
            description=None,
            tags=None,
            dag=None,
            tasks=None,
        ),
        StageFlow(
            name="cf_test_dag_7",
            type=FlowType.CONTROL,
            description=None,
            tags=None,
            dag=None,
            tasks=None,
        ),
        StageFlow(
            name="wrk_test_dag_7",
            type=FlowType.WORK,
            description=None,
            tags=None,
            dag=None,
            tasks=None,
        ),
    ]


@fixture(scope="module")
def stage_flows() -> Sequence[StageFlow]:
    return [
        StageFlow(
            name="cf_master_test",
            type=FlowType.MASTER,
            description=None,
            tags=None,
            dag=None,
            tasks=None,
        ),
        StageFlow(
            name="cf_test_1",
            type=FlowType.CONTROL,
            description=None,
            tags=None,
            dag=None,
            tasks=None,
        ),
        StageFlow(
            name="wf_test_1",
            type=FlowType.WORK,
            description=None,
            tags=None,
            dag=None,
            tasks=None,
        ),
        StageFlow(
            name="cf_test_2_1",
            type=FlowType.CONTROL,
            description=None,
            tags=None,
            dag=None,
            tasks=None,
        ),
        StageFlow(
            name="wrk_test_2_1",
            type=FlowType.WORK,
            description=None,
            tags=None,
            dag=None,
            tasks=None,
        ),
        StageFlow(
            name="cf_test_2_2",
            type=FlowType.CONTROL,
            description=None,
            tags=None,
            dag=None,
            tasks=None,
        ),
        StageFlow(
            name="wrk_test_2_2",
            type=FlowType.WORK,
            description=None,
            tags=None,
            dag=None,
            tasks=None,
        ),
    ]


@fixture(scope="module")
def stage_master_flows() -> Sequence[StageMasterFlow]:
    return [
        StageMasterFlow(name="cf_master_test:group_test_1"),
        StageMasterFlow(name="cf_master_test:group_test_2"),
    ]


@fixture(scope="module")
def stage_master_flow_links() -> Sequence[StageMasterFlowLink]:
    return [
        StageMasterFlowLink(
            owner_flow_name="cf_master_test",
            source_flow_name="cf_master_test",
            target_flow_name="cf_master_test:group_test_1",
        ),
        StageMasterFlowLink(
            owner_flow_name="cf_master_test:group_test_1",
            source_flow_name="cf_master_test:group_test_1",
            target_flow_name="cf_test_1",
        ),
        StageMasterFlowLink(
            owner_flow_name="cf_master_test:group_test_1",
            source_flow_name="cf_test_1",
            target_flow_name=None,
        ),
        StageMasterFlowLink(
            owner_flow_name="cf_master_test",
            source_flow_name="cf_master_test:group_test_1",
            target_flow_name="cf_master_test:group_test_2",
        ),
        StageMasterFlowLink(
            owner_flow_name="cf_master_test:group_test_2",
            source_flow_name="cf_master_test:group_test_2",
            target_flow_name="cf_test_2_1",
        ),
        StageMasterFlowLink(
            owner_flow_name="cf_master_test:group_test_2",
            source_flow_name="cf_test_2_1",
            target_flow_name=None,
        ),
        StageMasterFlowLink(
            owner_flow_name="cf_master_test:group_test_2",
            source_flow_name="cf_test_2_1",
            target_flow_name="cf_test_2_2",
        ),
        StageMasterFlowLink(
            owner_flow_name="cf_master_test:group_test_2",
            source_flow_name="cf_test_2_2",
            target_flow_name=None,
        ),
    ]


@fixture(scope="module")
def new_stage_flows() -> Sequence[StageFlow]:
    return [
        StageFlow(
            name="cf_master_test",
            type=FlowType.MASTER,
            description=None,
            tags=None,
            dag=None,
            tasks=None,
        ),
        StageFlow(
            name="cf_test_1",
            type=FlowType.CONTROL,
            description=None,
            tags=None,
            dag=None,
            tasks=None,
        ),
        StageFlow(
            name="wf_test_1",
            type=FlowType.WORK,
            description=None,
            tags=None,
            dag=None,
            tasks=None,
        ),
        StageFlow(
            name="cf_test_2_1",
            type=FlowType.CONTROL,
            description=None,
            tags=None,
            dag=None,
            tasks=None,
        ),
        StageFlow(
            name="wrk_test_2_1",
            type=FlowType.WORK,
            description=None,
            tags=None,
            dag=None,
            tasks=None,
        ),
        StageFlow(
            name="cf_test_2_0",
            type=FlowType.CONTROL,
            description=None,
            tags=None,
            dag=None,
            tasks=None,
        ),
        StageFlow(
            name="wrk_test_2_0",
            type=FlowType.WORK,
            description=None,
            tags=None,
            dag=None,
            tasks=None,
        ),
    ]


@fixture(scope="module")
def new_stage_master_flows() -> Sequence[StageMasterFlow]:
    return [
        StageMasterFlow(name="cf_master_test:group_test_1"),
        StageMasterFlow(name="cf_master_test:group_test_2"),
    ]


@fixture(scope="module")
def new_stage_master_flow_links() -> Sequence[StageMasterFlowLink]:
    return [
        StageMasterFlowLink(
            owner_flow_name="cf_master_test",
            source_flow_name="cf_master_test",
            target_flow_name="cf_master_test:group_test_1",
        ),
        StageMasterFlowLink(
            owner_flow_name="cf_master_test:group_test_1",
            source_flow_name="cf_master_test:group_test_1",
            target_flow_name="cf_test_1",
        ),
        StageMasterFlowLink(
            owner_flow_name="cf_master_test:group_test_1",
            source_flow_name="cf_test_1",
            target_flow_name=None,
        ),
        StageMasterFlowLink(
            owner_flow_name="cf_master_test",
            source_flow_name="cf_master_test:group_test_1",
            target_flow_name="cf_master_test:group_test_2",
        ),
        StageMasterFlowLink(
            owner_flow_name="cf_master_test:group_test_2",
            source_flow_name="cf_master_test:group_test_2",
            target_flow_name="cf_test_2_1",
        ),
        StageMasterFlowLink(
            owner_flow_name="cf_master_test:group_test_2",
            source_flow_name="cf_test_2_1",
            target_flow_name=None,
        ),
        StageMasterFlowLink(
            owner_flow_name="cf_master_test:group_test_2",
            source_flow_name="cf_master_test:group_test_2",
            target_flow_name="cf_test_2_0",
        ),
        StageMasterFlowLink(
            owner_flow_name="cf_master_test:group_test_2",
            source_flow_name="cf_test_2_0",
            target_flow_name=None,
        ),
    ]


@fixture(scope="module")
def table_id() -> str:
    return 32 * "0"


@fixture(scope="function")
def truncate_flow(db_session: Session):
    db_session.execute(text("TRUNCATE dict.dict_code_delivery"))
    db_session.execute(text("TRUNCATE metamodel.bridge_vcs_repository_version"))
    db_session.execute(text("TRUNCATE metamodel.bridge_version"))
    db_session.execute(text("TRUNCATE metamodel.bridge_flow"))
    db_session.execute(text("TRUNCATE metamodel.link_flow"))
    db_session.commit()
