from helpers import date_time
from metaloader_rest_api import etl
from metaloader_rest_api.common_model import LAST_DATE
from metaloader_rest_api.version_repository import VersionRepository
from pytest import fixture
from sqlalchemy import text
from sqlalchemy.orm import Session


def test_merge_empty_stg_and_empty_bridge_table(
    db_session,
    session_resource,
    core_version_id,
    bridge_table,
    bridge_stage,
    table_view_builder,
):
    with session_resource:
        etl.merge(
            session=db_session,
            version_id=core_version_id,
            effective_date=date_time(2024, 1, 1),
            table=bridge_table,
            stage=bridge_stage,
            primary_key="test_rk_1",
            keys=["test_name", "test_type_rk"],
            values=["test_desc"],
        )

    table = (
        table_view_builder()
        .table(bridge_table)
        .columns(
            "test_name",
            "test_type_rk",
            "test_desc",
            "version_rk",
            "deleted_flg",
            "effective_from_dttm",
            "effective_to_dttm",
        )
    )

    assert table.data().from_data() == table.from_table()


def test_merge_empty_stg_and_empty_link_table(
    db_session,
    session_resource,
    core_version_id,
    link_table,
    link_stage,
    table_view_builder,
):
    with session_resource:
        etl.merge(
            session=db_session,
            version_id=core_version_id,
            effective_date=date_time(2024, 1, 1),
            table=link_table,
            stage=link_stage,
            primary_key=None,
            keys=["test_rk_1", "test_rk_2", "test_type_rk"],
            values=["test_desc"],
        )

    table = (
        table_view_builder()
        .table(link_table)
        .columns(
            "test_rk_1",
            "test_rk_2",
            "test_type_rk",
            "test_desc",
            "version_rk",
            "deleted_flg",
            "effective_from_dttm",
            "effective_to_dttm",
        )
    )

    assert table.data().from_data() == table.from_table()


def test_merge_stg_and_empty_bridge_table(
    db_session,
    session_resource,
    core_version_id,
    bridge_table,
    bridge_stage,
    table_view_builder,
):
    table_view_builder().table(bridge_stage).columns(
        "test_type_rk", "test_name", "test_desc"
    ).data(
        (
            (1, "record_1", None),
            (2, "record_2", "test_description"),
            (3, "record_3", None),
        )
    ).insert()

    table = (
        table_view_builder()
        .table(bridge_table)
        .columns(
            "test_type_rk",
            "test_name",
            "test_desc",
            "version_rk",
            "deleted_flg",
            "effective_from_dttm",
            "effective_to_dttm",
        )
    )

    effective_date = date_time(2024, 1, 1)
    with session_resource:
        etl.merge(
            session=db_session,
            version_id=core_version_id,
            effective_date=effective_date,
            table=bridge_table,
            stage=bridge_stage,
            primary_key="test_rk_1",
            keys=["test_name", "test_type_rk"],
            values=["test_desc"],
        )

    # fmt: off
    expected = table.data(
        (
            (1, "record_1", None, core_version_id, False, effective_date, LAST_DATE),
            (2, "record_2", "test_description", core_version_id, False, effective_date, LAST_DATE),
            (3, "record_3", None, core_version_id, False, effective_date, LAST_DATE),
        )
    ).from_data()
    # fmt: on
    actual = table.from_table("test_name")

    assert expected == actual


def test_merge_stg_and_empty_link_table(
    db_session,
    session_resource,
    core_version_id,
    link_table,
    link_stage,
    table_view_builder,
):
    table_view_builder().table(link_stage).columns(
        "test_rk_1", "test_rk_2", "test_type_rk", "test_desc"
    ).data(
        (
            (1, 5, 1, None),
            (2, 10, 2, "test_description"),
            (3, 15, 3, None),
        )
    ).insert()

    table = (
        table_view_builder()
        .table(link_table)
        .columns(
            "test_rk_1",
            "test_rk_2",
            "test_type_rk",
            "test_desc",
            "version_rk",
            "deleted_flg",
            "effective_from_dttm",
            "effective_to_dttm",
        )
    )

    effective_date = date_time(2024, 1, 1)
    with session_resource:
        etl.merge(
            session=db_session,
            version_id=core_version_id,
            effective_date=effective_date,
            table=link_table,
            stage=link_stage,
            primary_key=None,
            keys=["test_rk_1", "test_rk_2", "test_type_rk"],
            values=["test_desc"],
        )

    # fmt: off
    expected = table.data(
        (
            (1, 5, 1, None, core_version_id, False, effective_date, LAST_DATE),
            (2, 10, 2, "test_description", core_version_id, False, effective_date, LAST_DATE),
            (3, 15, 3, None, core_version_id, False, effective_date, LAST_DATE),
        )
    ).from_data()
    # fmt: on
    actual = table.from_table("test_rk_1", "test_rk_2")

    assert expected == actual


def test_merge_empty_stg_and_bridge_table(
    db_session,
    session_resource,
    core_module_id,
    core_version_id,
    bridge_table,
    bridge_stage,
    table_view_builder,
):
    table = (
        table_view_builder()
        .table(bridge_table)
        .columns(
            "test_rk_1",
            "test_type_rk",
            "test_name",
            "test_desc",
            "version_rk",
            "deleted_flg",
            "effective_from_dttm",
            "effective_to_dttm",
        )
    )
    # fmt: off
    table.data(
        (
            (1, 1, "record_1", None, core_version_id, False, date_time(2024, 10, 1), LAST_DATE),
            (2, 2, "record_2", None, core_version_id, False, date_time(2024, 10, 1), date_time(2024, 10, 22)),
            (2, 2, "record_2", None, core_version_id, True, date_time(2024, 10, 22), LAST_DATE),
            (3, 3, "record_3", None, core_version_id, False, date_time(2024, 10, 1), date_time(2024, 11, 1)),
            (3, 3, "record_3", "test_description", core_version_id, False, date_time(2024, 11, 1), LAST_DATE),
        )
    ).insert()
    # fmt: on

    effective_date = date_time(2024, 12, 10)
    with session_resource:
        etl.merge(
            session=db_session,
            version_id=core_version_id,
            effective_date=effective_date,
            table=bridge_table,
            stage=bridge_stage,
            primary_key="test_rk_1",
            keys=["test_name", "test_type_rk"],
            values=["test_desc"],
        )

    # fmt: off
    expected = table.data(
        (
            (1, 1, "record_1", None, core_version_id, False, date_time(2024, 10, 1), effective_date),
            (1, 1, "record_1", None, core_version_id, True, effective_date, LAST_DATE),
            (2, 2, "record_2", None, core_version_id, False, date_time(2024, 10, 1), date_time(2024, 10, 22)),
            (2, 2, "record_2", None, core_version_id, True, date_time(2024, 10, 22), LAST_DATE),
            (3, 3, "record_3", None, core_version_id, False, date_time(2024, 10, 1), date_time(2024, 11, 1)),
            (3, 3, "record_3", "test_description", core_version_id, False, date_time(2024, 11, 1), effective_date),
            (3, 3, "record_3", "test_description", core_version_id, True, effective_date, LAST_DATE),
        )
    ).from_data()
    # fmt: on
    actual = table.from_table(
        "test_rk_1", "test_name", "test_type_rk", "effective_from_dttm"
    )

    assert expected == actual


def test_merge_empty_stg_and_link_table(
    db_session,
    session_resource,
    core_module_id,
    core_version_id,
    link_table,
    link_stage,
    table_view_builder,
):
    table = (
        table_view_builder()
        .table(link_table)
        .columns(
            "test_rk_1",
            "test_rk_2",
            "test_type_rk",
            "test_desc",
            "version_rk",
            "deleted_flg",
            "effective_from_dttm",
            "effective_to_dttm",
        )
    )
    # fmt: off
    table.data(
        (
            (1, 3, 1, None, core_version_id, False, date_time(2024, 10, 1), LAST_DATE),
            (5, 10, 2, None, core_version_id, False, date_time(2024, 10, 1), date_time(2024, 10, 22)),
            (5, 10, 3, None, core_version_id, True, date_time(2024, 10, 22), LAST_DATE),
            (7, 15, 3, None, core_version_id, False, date_time(2024, 10, 1), date_time(2024, 11, 1)),
            (7, 15, 3, "test_description", core_version_id, False, date_time(2024, 11, 1), LAST_DATE),
        )
    ).insert()
    # fmt: on

    effective_date = date_time(2024, 12, 10)
    with session_resource:
        etl.merge(
            session=db_session,
            version_id=core_version_id,
            effective_date=effective_date,
            table=link_table,
            stage=link_stage,
            primary_key=None,
            keys=["test_rk_1", "test_rk_2", "test_type_rk"],
            values=["test_desc"],
        )

    # fmt: off
    expected = table.data(
        (
            (1, 3, 1, None, core_version_id, False, date_time(2024, 10, 1), effective_date),
            (1, 3, 1, None, core_version_id, True, effective_date, LAST_DATE),
            (5, 10, 2, None, core_version_id, False, date_time(2024, 10, 1), date_time(2024, 10, 22)),
            (5, 10, 3, None, core_version_id, True, date_time(2024, 10, 22), LAST_DATE),
            (7, 15, 3, None, core_version_id, False, date_time(2024, 10, 1), date_time(2024, 11, 1)),
            (7, 15, 3, "test_description", core_version_id, False, date_time(2024, 11, 1), effective_date),
            (7, 15, 3, "test_description", core_version_id, True, effective_date, LAST_DATE),
        )
    ).from_data()
    # fmt: on
    actual = table.from_table(
        "test_rk_1", "test_rk_2", "test_type_rk", "effective_from_dttm"
    )

    assert expected == actual


def test_merge_stg_and_bridge_table(
    db_session,
    session_resource,
    core_module_id,
    core_version_id,
    bridge_table,
    bridge_stage,
    table_view_builder,
):
    table = table_view_builder().table(bridge_table)
    # fmt: off
    table.columns(
        "test_rk_1",
        "test_type_rk",
        "test_name",
        "test_desc",
        "version_rk",
        "deleted_flg",
        "effective_from_dttm",
        "effective_to_dttm",
    ).data(
        (
            (1, 1, "record_1", None, core_version_id, False, date_time(2024, 10, 1), LAST_DATE),
            (2, 2, "record_2", None, core_version_id, False, date_time(2024, 10, 1), date_time(2024, 10, 22)),
            (2, 2, "record_2", None, core_version_id, True, date_time(2024, 10, 22), LAST_DATE),
            (3, 3, "record_3", None, core_version_id, False, date_time(2024, 10, 1), date_time(2024, 11, 1)),
            (3, 3, "record_3", "test_description", core_version_id, False, date_time(2024, 11, 1), LAST_DATE),
            (4, 1, "record_4", None, core_version_id, False, date_time(2024, 10, 1), date_time(2024, 11, 1)),
            (4, 1, "record_4", "test_description", core_version_id, True, date_time(2024, 11, 1), LAST_DATE),
        )
    ).insert()
    # fmt: on

    table_view_builder().table(bridge_stage).columns(
        "test_type_rk", "test_name", "test_desc"
    ).data(
        (
            (1, "record_1", "test_description"),
            (2, "record_2", "test_description"),
            (1, "record_4", "test_description_new"),
            (1, "record_5", "test_description"),
        )
    ).insert()

    effective_date = date_time(2024, 12, 10)
    with session_resource:
        etl.merge(
            session=db_session,
            version_id=core_version_id,
            effective_date=effective_date,
            table=bridge_table,
            stage=bridge_stage,
            primary_key="test_rk_1",
            keys=["test_name", "test_type_rk"],
            values=["test_desc"],
        )

    table = table.columns(
        "test_type_rk",
        "test_name",
        "test_desc",
        "version_rk",
        "deleted_flg",
        "effective_from_dttm",
        "effective_to_dttm",
    )
    # fmt: off
    expected = table.data(
        (
            (1, "record_1", None, core_version_id, False, date_time(2024, 10, 1), effective_date),
            (1, "record_1", "test_description", core_version_id, False, effective_date, LAST_DATE),
            (2, "record_2", None, core_version_id, False, date_time(2024, 10, 1), date_time(2024, 10, 22)),
            (2, "record_2", None, core_version_id, True, date_time(2024, 10, 22), effective_date),
            (2, "record_2", "test_description", core_version_id, False, effective_date, LAST_DATE),
            (3, "record_3", None, core_version_id, False, date_time(2024, 10, 1), date_time(2024, 11, 1)),
            (3, "record_3", "test_description", core_version_id, False, date_time(2024, 11, 1), effective_date),
            (3, "record_3", "test_description", core_version_id, True, effective_date, LAST_DATE),
            (1, "record_4", None, core_version_id, False, date_time(2024, 10, 1), date_time(2024, 11, 1)),
            (1, "record_4", "test_description", core_version_id, True, date_time(2024, 11, 1), effective_date),
            (1, "record_4", "test_description_new", core_version_id, False, effective_date, LAST_DATE),
            (1, "record_5", "test_description", core_version_id, False, effective_date, LAST_DATE),
        )
    ).from_data()
    # fmt: on
    actual = table.from_table(
        "test_rk_1", "test_name", "test_type_rk", "effective_from_dttm"
    )

    assert expected == actual


def test_merge_stg_and_link_table(
    db_session,
    session_resource,
    core_module_id,
    core_version_id,
    link_table,
    link_stage,
    table_view_builder,
):
    table = (
        table_view_builder()
        .table(link_table)
        .columns(
            "test_rk_1",
            "test_rk_2",
            "test_type_rk",
            "test_desc",
            "version_rk",
            "deleted_flg",
            "effective_from_dttm",
            "effective_to_dttm",
        )
    )
    # fmt: off
    table.data(
        (
            (1, 5, 1, None, core_version_id, False, date_time(2024, 10, 1), LAST_DATE),
            (2, 13, 2, None, core_version_id, False, date_time(2024, 10, 1), date_time(2024, 10, 22)),
            (2, 13, 2, None, core_version_id, True, date_time(2024, 10, 22), LAST_DATE),
            (3, 22, 3, None, core_version_id, False, date_time(2024, 10, 1), date_time(2024, 11, 1)),
            (3, 22, 3, "test_description", core_version_id, False, date_time(2024, 11, 1), LAST_DATE),
            (4, 25, 1, None, core_version_id, False, date_time(2024, 10, 1), date_time(2024, 11, 1)),
            (4, 25, 1, None, core_version_id, True, date_time(2024, 11, 1), LAST_DATE),
        )
    ).insert()
    # fmt: on

    table_view_builder().table(link_stage).columns(
        "test_rk_1", "test_rk_2", "test_type_rk", "test_desc"
    ).data(
        (
            (1, 5, 1, "test_description"),
            (2, 13, 2, "test_description"),
            (4, 25, 1, "test_description"),
            (322, 228, 1, "test_description"),
        )
    ).insert()

    effective_date = date_time(2024, 12, 10)
    with session_resource:
        etl.merge(
            session=db_session,
            version_id=core_version_id,
            effective_date=effective_date,
            table=link_table,
            stage=link_stage,
            primary_key=None,
            keys=["test_rk_1", "test_rk_2", "test_type_rk"],
            values=["test_desc"],
        )

    # fmt: off
    expected = table.data(
        (
            (1, 5, 1, None, core_version_id, False, date_time(2024, 10, 1), effective_date),
            (1, 5, 1, "test_description", core_version_id, False, effective_date, LAST_DATE),
            (2, 13, 2, None, core_version_id, False, date_time(2024, 10, 1), date_time(2024, 10, 22)),
            (2, 13, 2, None, core_version_id, True, date_time(2024, 10, 22), effective_date),
            (2, 13, 2, "test_description", core_version_id, False, effective_date, LAST_DATE),
            (3, 22, 3, None, core_version_id, False, date_time(2024, 10, 1), date_time(2024, 11, 1)),
            (3, 22, 3, "test_description", core_version_id, False, date_time(2024, 11, 1), effective_date),
            (3, 22, 3, "test_description", core_version_id, True, effective_date, LAST_DATE),
            (4, 25, 1, None, core_version_id, False, date_time(2024, 10, 1), date_time(2024, 11, 1)),
            (4, 25, 1, None, core_version_id, True, date_time(2024, 11, 1), effective_date),
            (4, 25, 1, "test_description", core_version_id, False, effective_date, LAST_DATE),
            (322, 228, 1, "test_description", core_version_id, False, effective_date, LAST_DATE),
        )
    ).from_data()
    # fmt: on
    actual = table.from_table(
        "test_rk_1", "test_rk_2", "test_type_rk", "effective_from_dttm"
    )

    assert expected == actual


def test_merge_stg_and_bridge_table_with_filter_expression(
    db_session,
    session_resource,
    dm_module_id,
    dm_version_id,
    core_module_id,
    core_version_id,
    bridge_table_filter,
    bridge_stage_filter,
    table_view_builder,
):
    table = table_view_builder().table(bridge_table_filter)
    # fmt: off
    table.columns(
        "test_rk_1",
        "test_type_rk",
        "test_name",
        "test_desc",
        "filter_rk",
        "version_rk",
        "deleted_flg",
        "effective_from_dttm",
        "effective_to_dttm",
    ).data(
        (
            (1, 1, "dm_record_1", None, dm_module_id, dm_version_id, False, date_time(2024, 10, 1), LAST_DATE),
            (2, 2, "dm_record_2", None, dm_module_id, dm_version_id, False, date_time(2024, 10, 1), date_time(2024, 10, 22)),
            (2, 2, "dm_record_2", None, dm_module_id, dm_version_id, False, date_time(2024, 10, 22), LAST_DATE),
            (3, 3, "dm_record_3", None, dm_module_id, dm_version_id, True, date_time(2024, 10, 1), date_time(2024, 11, 1)),
            (3, 3, "dm_record_3", "test_description", dm_module_id, dm_version_id, False, date_time(2024, 11, 1), LAST_DATE),
            (4, 1, "dm_record_4", "test_description", dm_module_id, dm_version_id, True, date_time(2024, 11, 1), LAST_DATE),
        )
    ).insert()
    # fmt: on

    stage = (
        table_view_builder()
        .table(bridge_stage_filter)
        .columns(
            "test_type_rk",
            "test_name",
            "test_desc",
            "filter_rk",
        )
    )
    stage.data(
        (
            (1, "core_record_1", "test_description", core_module_id),
            (2, "core_record_2", None, core_module_id),
            (3, "core_record_3", None, core_module_id),
            (4, "core_record_4", "test_description", core_module_id),
        )
    ).insert()

    effective_date = date_time(2024, 12, 10)
    with session_resource:
        etl.merge(
            session=db_session,
            version_id=core_version_id,
            effective_date=effective_date,
            table=bridge_table_filter,
            stage=bridge_stage_filter,
            primary_key="test_rk_1",
            keys=["test_name", "test_type_rk"],
            values=["test_desc"],
            others=["filter_rk"],
            filter_expression="filter_rk = :filter_rk",
            params={"filter_rk": core_module_id},
        )

    table = (
        table_view_builder()
        .table(bridge_table_filter)
        .columns(
            "test_type_rk",
            "test_name",
            "test_desc",
            "filter_rk",
            "version_rk",
            "deleted_flg",
            "effective_from_dttm",
            "effective_to_dttm",
        )
    )
    # fmt: off
    expected = table.data(
        (
            (1, "dm_record_1", None, dm_module_id, dm_version_id, False, date_time(2024, 10, 1), LAST_DATE),
            (2, "dm_record_2", None, dm_module_id, dm_version_id, False, date_time(2024, 10, 1), date_time(2024, 10, 22)),
            (2, "dm_record_2", None, dm_module_id, dm_version_id, False, date_time(2024, 10, 22), LAST_DATE),
            (3, "dm_record_3", None, dm_module_id, dm_version_id, True, date_time(2024, 10, 1), date_time(2024, 11, 1)),
            (3, "dm_record_3", "test_description", dm_module_id, dm_version_id, False, date_time(2024, 11, 1), LAST_DATE),
            (1, "dm_record_4", "test_description", dm_module_id, dm_version_id, True, date_time(2024, 11, 1), LAST_DATE),
            (1, "core_record_1", "test_description", core_module_id, core_version_id, False, effective_date, LAST_DATE),
            (2, "core_record_2", None, core_module_id, core_version_id, False, effective_date, LAST_DATE),
            (3, "core_record_3", None, core_module_id, core_version_id, False, effective_date, LAST_DATE),
            (4, "core_record_4", "test_description", core_module_id, core_version_id, False, effective_date, LAST_DATE),
        )
    ).from_data()
    # fmt: on
    actual = table.from_table(
        "filter_rk DESC",
        "test_rk_1",
        "test_name",
        "test_type_rk",
        "effective_from_dttm",
    )

    assert expected == actual

    # Добавляем инкремент
    stage.truncate()
    stage.data(
        (
            (2, "core_record_2", "test_description", core_module_id),
            (3, "core_record_3", "test_description", core_module_id),
        )
    ).insert()

    effective_date_increment = date_time(2024, 12, 20)
    with session_resource:
        etl.merge(
            session=db_session,
            version_id=core_version_id,
            effective_date=effective_date_increment,
            table=bridge_table_filter,
            stage=bridge_stage_filter,
            primary_key="test_rk_1",
            keys=["test_name", "test_type_rk"],
            values=["test_desc"],
            others=["filter_rk"],
            filter_expression="filter_rk = :filter_rk",
            params={"filter_rk": core_module_id},
        )

    # fmt: off
    expected = table.data(
        (
            (1, "dm_record_1", None, dm_module_id, dm_version_id, False, date_time(2024, 10, 1), LAST_DATE),
            (2, "dm_record_2", None, dm_module_id, dm_version_id, False, date_time(2024, 10, 1), date_time(2024, 10, 22)),
            (2, "dm_record_2", None, dm_module_id, dm_version_id, False, date_time(2024, 10, 22), LAST_DATE),
            (3, "dm_record_3", None, dm_module_id, dm_version_id, True, date_time(2024, 10, 1), date_time(2024, 11, 1)),
            (3, "dm_record_3", "test_description", dm_module_id, dm_version_id, False, date_time(2024, 11, 1), LAST_DATE),
            (1, "dm_record_4", "test_description", dm_module_id, dm_version_id, True, date_time(2024, 11, 1), LAST_DATE),
            (1, "core_record_1", "test_description", core_module_id, core_version_id, False, effective_date, effective_date_increment),
            (1, "core_record_1", "test_description", core_module_id, core_version_id, True, effective_date_increment, LAST_DATE),
            (2, "core_record_2", None, core_module_id, core_version_id, False, effective_date, effective_date_increment),
            (2, "core_record_2", "test_description", core_module_id, core_version_id, False, effective_date_increment, LAST_DATE),
            (3, "core_record_3", None, core_module_id, core_version_id, False, effective_date, effective_date_increment),
            (3, "core_record_3", "test_description", core_module_id, core_version_id, False, effective_date_increment, LAST_DATE),
            (4, "core_record_4", "test_description", core_module_id, core_version_id, False, effective_date, effective_date_increment),
            (4, "core_record_4", "test_description", core_module_id, core_version_id, True, effective_date_increment, LAST_DATE),
        )
    ).from_data()
    # fmt: on
    actual = table.from_table(
        "filter_rk DESC",
        "test_rk_1",
        "test_name",
        "test_type_rk",
        "effective_from_dttm",
    )

    assert expected == actual


def test_merge_stg_and_link_table_with_filter_expression(
    db_session,
    session_resource,
    dm_module_id,
    dm_version_id,
    core_module_id,
    core_version_id,
    link_table_filter,
    link_stage_filter,
    table_view_builder,
):
    table = (
        table_view_builder()
        .table(link_table_filter)
        .columns(
            "test_rk_1",
            "test_rk_2",
            "test_type_rk",
            "test_desc",
            "filter_rk",
            "version_rk",
            "deleted_flg",
            "effective_from_dttm",
            "effective_to_dttm",
        )
    )
    # fmt: off
    table.data(
        (
            (1, 5, 1, "dm_test_description", dm_module_id, dm_version_id, False, date_time(2024, 10, 1), LAST_DATE),
            (2, 13, 2, "dm_test_description", dm_module_id, dm_version_id, False, date_time(2024, 10, 1), date_time(2024, 10, 22)),
            (2, 13, 2, "dm_test_description", dm_module_id, dm_version_id, True, date_time(2024, 10, 22), LAST_DATE),
            (3, 22, 3, "dm_test_description", dm_module_id, dm_version_id, False, date_time(2024, 10, 1), date_time(2024, 11, 1)),
            (3, 22, 3, "dm_test_description", dm_module_id, dm_version_id, False, date_time(2024, 11, 1), LAST_DATE),
            (4, 25, 1, "dm_test_description", dm_module_id, dm_version_id, False, date_time(2024, 10, 1), date_time(2024, 11, 1)),
            (4, 25, 1, "dm_test_description", dm_module_id, dm_version_id, True, date_time(2024, 11, 1), LAST_DATE),
        )
    ).insert()
    # fmt: on

    stage = (
        table_view_builder()
        .table(link_stage_filter)
        .columns("test_rk_1", "test_rk_2", "test_type_rk", "test_desc", "filter_rk")
    )
    stage.data(
        (
            (1, 5, 1, "core_test_description", core_module_id),
            (2, 13, 2, "core_test_description", core_module_id),
            (4, 25, 1, "core_test_description", core_module_id),
            (322, 228, 1, "core_test_description", core_module_id),
        )
    ).insert()

    effective_date = date_time(2024, 12, 10)
    with session_resource:
        etl.merge(
            session=db_session,
            version_id=core_version_id,
            effective_date=effective_date,
            table=link_table_filter,
            stage=link_stage_filter,
            primary_key=None,
            keys=["test_rk_1", "test_rk_2", "test_type_rk"],
            values=["test_desc"],
            others=["filter_rk"],
            filter_expression="filter_rk = :filter_rk",
            params={"filter_rk": core_module_id},
        )

    # fmt: off
    expected = table.data(
        (
            (1, 5, 1, "dm_test_description", dm_module_id, dm_version_id, False, date_time(2024, 10, 1), LAST_DATE),
            (2, 13, 2, "dm_test_description", dm_module_id, dm_version_id, False, date_time(2024, 10, 1), date_time(2024, 10, 22)),
            (2, 13, 2, "dm_test_description", dm_module_id, dm_version_id, True, date_time(2024, 10, 22), LAST_DATE),
            (3, 22, 3, "dm_test_description", dm_module_id, dm_version_id, False, date_time(2024, 10, 1), date_time(2024, 11, 1)),
            (3, 22, 3, "dm_test_description", dm_module_id, dm_version_id, False, date_time(2024, 11, 1), LAST_DATE),
            (4, 25, 1, "dm_test_description", dm_module_id, dm_version_id, False, date_time(2024, 10, 1), date_time(2024, 11, 1)),
            (4, 25, 1, "dm_test_description", dm_module_id, dm_version_id, True, date_time(2024, 11, 1), LAST_DATE),
            (1, 5, 1, "core_test_description", core_module_id, core_version_id, False, effective_date, LAST_DATE),
            (2, 13, 2, "core_test_description", core_module_id, core_version_id, False, effective_date, LAST_DATE),
            (4, 25, 1, "core_test_description", core_module_id, core_version_id, False, effective_date, LAST_DATE),
            (322, 228, 1, "core_test_description", core_module_id, core_version_id, False, effective_date, LAST_DATE),
        )
    ).from_data()
    # fmt: on
    actual = table.from_table(
        "filter_rk DESC",
        "test_rk_1",
        "test_rk_2",
        "test_type_rk",
        "effective_from_dttm",
    )

    assert expected == actual

    # Добавляем инкремент
    stage.truncate()
    stage.data(
        (
            (2, 13, 2, "core_test_description_new", core_module_id),
            (4, 25, 1, "core_test_description_new", core_module_id),
        )
    ).insert()

    effective_date_increment = date_time(2024, 12, 20)
    with session_resource:
        etl.merge(
            session=db_session,
            version_id=core_version_id,
            effective_date=effective_date_increment,
            table=link_table_filter,
            stage=link_stage_filter,
            primary_key=None,
            keys=["test_rk_1", "test_rk_2", "test_type_rk"],
            values=["test_desc"],
            others=["filter_rk"],
            filter_expression="filter_rk = :filter_rk",
            params={"filter_rk": core_module_id},
        )

    # fmt: off
    expected = table.data(
        (
            (1, 5, 1, "dm_test_description", dm_module_id, dm_version_id, False, date_time(2024, 10, 1), LAST_DATE),
            (2, 13, 2, "dm_test_description", dm_module_id, dm_version_id, False, date_time(2024, 10, 1), date_time(2024, 10, 22)),
            (2, 13, 2, "dm_test_description", dm_module_id, dm_version_id, True, date_time(2024, 10, 22), LAST_DATE),
            (3, 22, 3, "dm_test_description", dm_module_id, dm_version_id, False, date_time(2024, 10, 1), date_time(2024, 11, 1)),
            (3, 22, 3, "dm_test_description", dm_module_id, dm_version_id, False, date_time(2024, 11, 1), LAST_DATE),
            (4, 25, 1, "dm_test_description", dm_module_id, dm_version_id, False, date_time(2024, 10, 1), date_time(2024, 11, 1)),
            (4, 25, 1, "dm_test_description", dm_module_id, dm_version_id, True, date_time(2024, 11, 1), LAST_DATE),
            (1, 5, 1, "core_test_description", core_module_id, core_version_id, False, effective_date, effective_date_increment),
            (1, 5, 1, "core_test_description", core_module_id, core_version_id, True, effective_date_increment, LAST_DATE),
            (2, 13, 2, "core_test_description", core_module_id, core_version_id, False, effective_date, effective_date_increment),
            (2, 13, 2, "core_test_description_new", core_module_id, core_version_id, False, effective_date_increment, LAST_DATE),
            (4, 25, 1, "core_test_description", core_module_id, core_version_id, False, effective_date, effective_date_increment),
            (4, 25, 1, "core_test_description_new", core_module_id, core_version_id, False, effective_date_increment, LAST_DATE),
            (322, 228, 1, "core_test_description", core_module_id, core_version_id, False, effective_date, effective_date_increment),
            (322, 228, 1, "core_test_description", core_module_id, core_version_id, True, effective_date_increment, LAST_DATE),
        )
    ).from_data()
    # fmt: on
    actual = table.from_table(
        "filter_rk DESC",
        "test_rk_1",
        "test_rk_2",
        "test_type_rk",
        "effective_from_dttm",
    )

    assert expected == actual


def add_module_id(
    db_session,
    name: str,
    id: int,
) -> None:
    db_session.execute(
        statement=text("""
            INSERT INTO dict.dict_code_delivery(code_delivery_rk
                                              , code_delivery_cd
                                              , code_delivery_name
                                              , is_ddl_flg
                                              , is_flow_flg
                                              , is_config_flg
                                              , deleted_flg)
            VALUES (:code_delivery_rk
                  , :code_delivery_name
                  , :code_delivery_name
                  , FALSE
                  , TRUE
                  , FALSE
                  , FALSE)
        """),
        params={
            "code_delivery_rk": id,
            "code_delivery_name": name,
        },
    )
    db_session.commit()


def add_version_id(
    db_session,
    session_resource,
    module_id: int,
) -> int:
    with session_resource:
        version_repository = VersionRepository(db_session)
        return version_repository.put(
            module_id,
            major=1,
            minor=2,
            fix=3,
        )


@fixture(scope="function")
def bridge_stage(db_session):
    schema_name = "stg"
    table_name = "test_1"
    db_session.execute(text(f"""DROP TABLE IF EXISTS {schema_name}.{table_name}"""))
    db_session.execute(
        text(f"""
        CREATE TABLE {schema_name}.{table_name}(
            test_type_rk            BIGINT
          , test_name               TEXT
          , test_desc               TEXT
        )
        """)
    )
    db_session.commit()
    return f"{schema_name}.{table_name}"


@fixture(scope="function")
def link_stage(db_session):
    schema_name = "stg"
    table_name = "test_2"
    db_session.execute(text(f"""DROP TABLE IF EXISTS {schema_name}.{table_name}"""))
    db_session.execute(
        text(f"""
        CREATE TABLE {schema_name}.{table_name}(
            test_rk_1               BIGINT
          , test_rk_2               BIGINT
          , test_type_rk            BIGINT
          , test_desc               TEXT
        )
        """)
    )
    db_session.commit()
    return f"{schema_name}.{table_name}"


@fixture(scope="function")
def bridge_stage_filter(db_session):
    schema_name = "stg"
    table_name = "test_1"
    db_session.execute(text(f"""DROP TABLE IF EXISTS {schema_name}.{table_name}"""))
    db_session.execute(
        text(f"""
        CREATE TABLE {schema_name}.{table_name}(
            test_type_rk            BIGINT
          , test_name               TEXT
          , test_desc               TEXT
          , filter_rk               BIGINT
        )
        """)
    )
    db_session.commit()
    return f"{schema_name}.{table_name}"


@fixture(scope="function")
def link_stage_filter(db_session):
    schema_name = "stg"
    table_name = "test_2"
    db_session.execute(text(f"""DROP TABLE IF EXISTS {schema_name}.{table_name}"""))
    db_session.execute(
        text(f"""
        CREATE TABLE {schema_name}.{table_name}(
            test_rk_1               BIGINT
          , test_rk_2               BIGINT
          , test_type_rk            BIGINT
          , test_desc               TEXT
          , filter_rk               BIGINT
        )
        """)
    )
    db_session.commit()
    return f"{schema_name}.{table_name}"


@fixture(scope="function")
def bridge_table(db_session):
    schema_name = "metamodel"
    table_name = "test_1"
    db_session.execute(text(f"""DROP TABLE IF EXISTS {schema_name}.{table_name}"""))
    db_session.execute(
        text(f"""
        CREATE TABLE {schema_name}.{table_name}(
            test_rk_1               BIGINT
          , test_type_rk            BIGINT
          , test_name               TEXT
          , test_desc               TEXT
          , effective_from_dttm     TIMESTAMP WITH TIME ZONE    NOT NULL
          , effective_to_dttm       TIMESTAMP WITH TIME ZONE    NOT NULL
          , version_rk              BIGINT                      NOT NULL
          , deleted_flg             BOOLEAN                     NOT NULL
        )
    """)
    )
    db_session.commit()
    return f"{schema_name}.{table_name}"


@fixture(scope="function")
def link_table(db_session):
    schema_name = "metamodel"
    table_name = "test_1"
    db_session.execute(text(f"""DROP TABLE IF EXISTS {schema_name}.{table_name}"""))
    db_session.execute(
        text(f"""
        CREATE TABLE {schema_name}.{table_name}(
            test_rk_1               BIGINT
          , test_rk_2               BIGINT
          , test_type_rk            BIGINT
          , test_desc               TEXT
          , effective_from_dttm     TIMESTAMP WITH TIME ZONE    NOT NULL
          , effective_to_dttm       TIMESTAMP WITH TIME ZONE    NOT NULL
          , version_rk              BIGINT                      NOT NULL
          , deleted_flg             BOOLEAN                     NOT NULL
        )
    """)
    )
    db_session.commit()
    return f"{schema_name}.{table_name}"


@fixture(scope="function")
def bridge_table_filter(db_session):
    schema_name = "metamodel"
    table_name = "test_1"
    db_session.execute(text(f"""DROP TABLE IF EXISTS {schema_name}.{table_name}"""))
    db_session.execute(
        text(f"""
        CREATE TABLE {schema_name}.{table_name}(
            test_rk_1               BIGINT
          , test_type_rk            BIGINT
          , filter_rk               BIGINT
          , test_name               TEXT
          , test_desc               TEXT
          , effective_from_dttm     TIMESTAMP WITH TIME ZONE    NOT NULL
          , effective_to_dttm       TIMESTAMP WITH TIME ZONE    NOT NULL
          , version_rk              BIGINT                      NOT NULL
          , deleted_flg             BOOLEAN                     NOT NULL
        )
    """)
    )
    db_session.commit()
    return f"{schema_name}.{table_name}"


@fixture(scope="function")
def link_table_filter(db_session):
    schema_name = "metamodel"
    table_name = "test_1"
    db_session.execute(text(f"""DROP TABLE IF EXISTS {schema_name}.{table_name}"""))
    db_session.execute(
        text(f"""
        CREATE TABLE {schema_name}.{table_name}(
            test_rk_1               BIGINT
          , test_rk_2               BIGINT
          , test_type_rk            BIGINT
          , filter_rk               BIGINT
          , test_desc               TEXT
          , effective_from_dttm     TIMESTAMP WITH TIME ZONE    NOT NULL
          , effective_to_dttm       TIMESTAMP WITH TIME ZONE    NOT NULL
          , version_rk              BIGINT                      NOT NULL
          , deleted_flg             BOOLEAN                     NOT NULL
        )
    """)
    )
    db_session.commit()
    return f"{schema_name}.{table_name}"


@fixture(scope="function", autouse=True)
def truncate_tables(db_session: Session):
    db_session.execute(text("TRUNCATE dict.dict_code_delivery"))
    db_session.execute(text("TRUNCATE metamodel.bridge_version"))
    db_session.commit()


@fixture(scope="function")
def core_version_id(
    db_session,
    session_resource,
    core_module_id,
) -> int:
    return add_version_id(db_session, session_resource, core_module_id)


@fixture(scope="function")
def dm_version_id(
    db_session,
    session_resource,
    dm_module_id,
) -> int:
    return add_version_id(db_session, session_resource, dm_module_id)


@fixture(scope="function")
def core_module_id(db_session) -> int:
    id = 1
    add_module_id(db_session, "core", id)
    return id


@fixture(scope="function")
def dm_module_id(db_session) -> int:
    id = 2
    add_module_id(db_session, "dm", id)
    return id
