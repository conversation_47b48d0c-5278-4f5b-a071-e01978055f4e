from datetime import datetime, timezone
from uuid import UUID

from metaloader_rest_api.ceh_module import ceh_module_api
from metaloader_rest_api.ceh_module.ceh_module_api import LoadModulesParam, LoadModulesParams
from pytest import fixture
from sqlalchemy import text


def test_init_load_module(
    db_session,
    effective_date_init,
    load_id,
    page_size,
    data_init,
    truncate_tables,
    source,
):
    ceh_module_api.load_modules(
        session=db_session,
        load_id=load_id,
        effective_date=effective_date_init,
        params=data_init,
    )
    db_session.commit()

    rows = db_session.execute(text("SELECT * FROM dict.dict_code_delivery")).fetchall()
    assert len(rows) == 3

    rows = db_session.execute(
        text("SELECT * FROM metamodel.link_source_delivery")
    ).fetchall()
    assert len(rows) == 3

    ceh_module_api.load_modules(
        session=db_session,
        load_id=load_id,
        effective_date=effective_date_init,
        params=data_init,
    )
    db_session.commit()

    rows = db_session.execute(text("SELECT * FROM dict.dict_code_delivery")).fetchall()
    assert len(rows) == 3

    rows = db_session.execute(
        text("SELECT * FROM metamodel.link_source_delivery")
    ).fetchall()
    assert len(rows) == 3


def test_incr_load_module(
    db_session,
    effective_date_init,
    effective_date_incr,
    load_id,
    page_size,
    data_init,
    data_incr,
    truncate_tables,
    source,
):
    ceh_module_api.load_modules(
        session=db_session,
        load_id=load_id,
        effective_date=effective_date_init,
        params=data_init,
    )
    db_session.commit()

    rows = db_session.execute(text("SELECT * FROM dict.dict_code_delivery")).fetchall()
    assert len(rows) == 3

    rows = db_session.execute(
        text("SELECT * FROM metamodel.link_source_delivery")
    ).fetchall()
    assert len(rows) == 3

    ceh_module_api.load_modules(
        session=db_session,
        load_id=load_id,
        effective_date=effective_date_incr,
        params=data_incr,
    )
    db_session.commit()

    rows = db_session.execute(text("SELECT * FROM dict.dict_code_delivery")).fetchall()
    assert len(rows) == 4

    rows = db_session.execute(
        text("SELECT * FROM metamodel.link_source_delivery")
    ).fetchall()
    assert len(rows) == 5


@fixture(scope="session")
def effective_date_init() -> datetime:
    return datetime(2025, 1, 1, tzinfo=timezone.utc)


@fixture(scope="session")
def effective_date_incr() -> datetime:
    return datetime(2025, 1, 3, tzinfo=timezone.utc)


@fixture(scope="session")
def load_id() -> UUID:
    return UUID("00000000-0000-0000-0000-000000000000")


@fixture(scope="session")
def page_size() -> int:
    return 3


@fixture(scope="function")
def source(db_session) -> None:
    db_session.execute(text("TRUNCATE TABLE metamodel.bridge_source"))
    db_session.execute(
        text("""
            INSERT INTO metamodel.bridge_source(source_rk
                                              , source_cd
                                              , ris_src_id
                                              , ris_src_code
                                              , effective_from_dttm
                                              , effective_to_dttm
                                              , deleted_flg)
                 VALUES (1, 'source_cd_1', 'ris_src_id_1', 'ris_src_code_1', '1900-01-01 +0', '2999-12-31 +0', FALSE)
        """)
    )
    db_session.commit()


@fixture(scope="function")
def truncate_tables(db_session):
    db_session.execute(text("TRUNCATE TABLE dict.dict_code_delivery"))
    db_session.execute(text("TRUNCATE TABLE metamodel.link_source_delivery"))
    db_session.commit()


@fixture(scope="session")
def data_init() -> LoadModulesParams:
    modules = [
        {
          "code_delivery_cd":   "code_delivery_cd_1",
          "code_delivery_name": "code_delivery_name_1",
          "code_delivery_desc": "code_delivery_desc_1",
          "ris_src_id":         "ris_src_id_1",
          "ris_src_code":       "ris_src_code_1",
          "is_ddl_flg":         True,
          "is_flow_flg":        True,
          "is_config_flg":      True,
        },
        {
          "code_delivery_cd":   "code_delivery_cd_2",
          "code_delivery_name": "code_delivery_name_2",
          "code_delivery_desc": "code_delivery_desc_2",
          "ris_src_id":         "ris_src_id_1",
          "ris_src_code":       "ris_src_code_2",
          "is_ddl_flg":         False,
          "is_flow_flg":        False,
          "is_config_flg":      False,
        },
        {
          "code_delivery_cd":   "code_delivery_cd_3",
          "code_delivery_name": "code_delivery_name_3",
          "code_delivery_desc": "code_delivery_desc_3",
          "ris_src_id":         "ris_src_id_1",
          "ris_src_code":       "ris_src_code_3",
          "is_ddl_flg":         True,
          "is_flow_flg":        True,
          "is_config_flg":      True,
        }
    ]
    return [LoadModulesParam(**module) for module in modules]


@fixture(scope="session")
def data_incr() -> LoadModulesParams:
    modules = [
        {
          "code_delivery_cd":   "code_delivery_cd_1",
          "code_delivery_name": "code_delivery_name_1",
          "code_delivery_desc": "code_delivery_desc_1",
          "ris_src_id":         "ris_src_id_1",
          "ris_src_code":       "ris_src_code_1",
          "is_ddl_flg":         True,
          "is_flow_flg":        True,
          "is_config_flg":      True,
        },
        {
          "code_delivery_cd":   "code_delivery_cd_3",
          "code_delivery_name": "code_delivery_name_3_edit",
          "code_delivery_desc": "code_delivery_desc_3",
          "ris_src_id":         "ris_src_id_1",
          "ris_src_code":       "ris_src_code_3_edit",
          "is_ddl_flg":         True,
          "is_flow_flg":        True,
          "is_config_flg":      True,
        },
        {
          "code_delivery_cd":   "code_delivery_cd_4",
          "code_delivery_name": "code_delivery_name_4",
          "code_delivery_desc": "code_delivery_desc_4",
          "ris_src_id":         "ris_src_id_1",
          "ris_src_code":       "ris_src_code_4",
          "is_ddl_flg":         False,
          "is_flow_flg":        False,
          "is_config_flg":      False,
        },
    ]
    return [LoadModulesParam(**module) for module in modules]
