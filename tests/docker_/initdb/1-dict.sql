create schema dict;

create domain dict.dname as varchar(250);

create table dict.dict_flow_type (
    flow_type_rk   smallint   not null primary key,
    flow_type_cd   dict.dname not null,
    flow_type_name dict.dname not null,
    flow_type_desc text
);
insert into dict.dict_flow_type (flow_type_rk, flow_type_cd, flow_type_name, flow_type_desc) values (-1, 'undef', 'undefined', 'не определено');
insert into dict.dict_flow_type (flow_type_rk, flow_type_cd, flow_type_name, flow_type_desc) values (1, 'wf', 'work', 'рабочий поток');
insert into dict.dict_flow_type (flow_type_rk, flow_type_cd, flow_type_name, flow_type_desc) values (2, 'cf', 'control', 'управляющий поток');
insert into dict.dict_flow_type (flow_type_rk, flow_type_cd, flow_type_name, flow_type_desc) values (3, 'mf', 'master', 'мастер поток');
insert into dict.dict_flow_type (flow_type_rk, flow_type_cd, flow_type_name, flow_type_desc) values (4, 'sem', 'semaphore', 'семафор (логический узел связности потоков)');
insert into dict.dict_flow_type (flow_type_rk, flow_type_cd, flow_type_name, flow_type_desc) values (5, 'sf', 'service', 'сервисный поток');


create table dict.dict_flow_release_status (
    flow_release_status_rk   smallint   not null primary key,
    flow_release_status_cd   dict.dname not null,
    flow_release_status_name dict.dname not null,
    flow_release_status_desc text
);
insert into dict.dict_flow_release_status (flow_release_status_rk, flow_release_status_cd, flow_release_status_name, flow_release_status_desc) values (1, 'deployed', 'deployed', 'Поток на проде и доступен для запусков');
insert into dict.dict_flow_release_status (flow_release_status_rk, flow_release_status_cd, flow_release_status_name, flow_release_status_desc) values (2, 'deprecated', 'deprecated', 'Поток может запускаться, но не может обновляться новыми релизами (его изменения и его включение в мастер-потоки блокируются)');
insert into dict.dict_flow_release_status (flow_release_status_rk, flow_release_status_cd, flow_release_status_name, flow_release_status_desc) values (3, 'undeployed', 'undeployed', 'Поток не может использоваться, удалён с прода');


create table dict.dict_flow_link_type (
    flow_link_type_rk   smallint   not null primary key,
    flow_link_type_cd   dict.dname not null,
    flow_link_type_name dict.dname not null,
    flow_link_type_desc text
);
insert into dict.dict_flow_link_type (flow_link_type_rk, flow_link_type_cd, flow_link_type_name, flow_link_type_desc) values (1, 'subscriber', 'subscriber relationship', 'Связность потоков одного уровня вложенности - рабочих потоков или мастер-потоков');
insert into dict.dict_flow_link_type (flow_link_type_rk, flow_link_type_cd, flow_link_type_name, flow_link_type_desc) values (2, 'control', 'control relationship', 'Связность контрольного и рабочего потоков');
insert into dict.dict_flow_link_type (flow_link_type_rk, flow_link_type_cd, flow_link_type_name, flow_link_type_desc) values (3, 'semaphore', 'semaphore relationship', 'Связность семафора с потоками');

create table dict.dict_code_delivery (
    code_delivery_rk    smallint    not null,
    code_delivery_cd    dict.dname  not null,
    code_delivery_name  dict.dname  not null,
    code_delivery_desc  text,
	is_ddl_flg          boolean     not null,
	is_flow_flg         boolean     not null,
    is_config_flg       boolean     not null,
    deleted_flg         boolean     not null,
    constraint dict_code_delivery_pkey
        primary key (code_delivery_rk)
);

create table dict.dict_vcs_repository (
	vcs_repository_rk       smallint    not null primary key,
	vcs_repository_cd       dict.dname  not null,
	vcs_repository_name     dict.dname  not null,
	vcs_repository_desc     text,
	vcs_repository_url      text
);
insert into dict.dict_vcs_repository (vcs_repository_rk, vcs_repository_cd, vcs_repository_name, vcs_repository_desc, vcs_repository_url) values (1, 'DTPL/ceh-config', 'DTPL/ceh-config', null, 'https://sfera.inno.local/sourcecode/projects/DTPL/repos/ceh-metamodel');
insert into dict.dict_vcs_repository (vcs_repository_rk, vcs_repository_cd, vcs_repository_name, vcs_repository_desc, vcs_repository_url) values (2, 'DTPL/ceh-etl', 'DTPL/ceh-etl', null, 'https://sfera.inno.local/sourcecode/projects/DTPL/repos/ceh-etl');

CREATE TABLE dict.dict_code_folder (
    code_folder_rk   SMALLINT   NOT NULL
        CONSTRAINT dict_code_folder_pk
            PRIMARY KEY,
    code_folder_cd   dict.dname NOT NULL,
    code_folder_name dict.dname,
    code_folder_desc TEXT,
    code_folder_path TEXT       NOT NULL
);
INSERT INTO dict.dict_code_folder (code_folder_rk, code_folder_cd, code_folder_name, code_folder_desc, code_folder_path) VALUES (1, 'rdv_control_flows', null, null, 'general_ledger/src_rdv/flow_dumps');
INSERT INTO dict.dict_code_folder (code_folder_rk, code_folder_cd, code_folder_name, code_folder_desc, code_folder_path) VALUES (2, 'rdv_work_flows', null, null, 'general_ledger/src_rdv/schema/work_flows');
INSERT INTO dict.dict_code_folder (code_folder_rk, code_folder_cd, code_folder_name, code_folder_desc, code_folder_path) VALUES (3, 'idl_flows', null, null, 'core/general_ledger/rdv_idl/flow_dumps');
INSERT INTO dict.dict_code_folder (code_folder_rk, code_folder_cd, code_folder_name, code_folder_desc, code_folder_path) VALUES (4, 'idl_flows', null, null, 'dm_pik_fd_cdmr/rdv_idl/flow_dumps');
INSERT INTO dict.dict_code_folder (code_folder_rk, code_folder_cd, code_folder_name, code_folder_desc, code_folder_path) VALUES (6, 'idl_flows', null, null, 'dm_sme_dakipr_sme/rdv_idl/flow_dumps');
INSERT INTO dict.dict_code_folder (code_folder_rk, code_folder_cd, code_folder_name, code_folder_desc, code_folder_path) VALUES (8, 'idl_flows', null, null, 'dm_pik_fd_dtrb/rdv_idl/flow_dumps');
INSERT INTO dict.dict_code_folder (code_folder_rk, code_folder_cd, code_folder_name, code_folder_desc, code_folder_path) VALUES (10, 'idl_flows', null, null, 'dm_pik_fd_dekv/rdv_idl/flow_dumps');
INSERT INTO dict.dict_code_folder (code_folder_rk, code_folder_cd, code_folder_name, code_folder_desc, code_folder_path) VALUES (12, 'idl_flows', null, null, 'dm_rb_crm_bcrm/rdv_idl/flow_dumps');
INSERT INTO dict.dict_code_folder (code_folder_rk, code_folder_cd, code_folder_name, code_folder_desc, code_folder_path) VALUES (14, 'idl_flows', null, null, 'dm_str_its76_bki/rdv_idl/flow_dumps');
INSERT INTO dict.dict_code_folder (code_folder_rk, code_folder_cd, code_folder_name, code_folder_desc, code_folder_path) VALUES (16, 'idl_flows', null, null, 'dm_str_its76_cre/rdv_idl/flow_dumps');
INSERT INTO dict.dict_code_folder (code_folder_rk, code_folder_cd, code_folder_name, code_folder_desc, code_folder_path) VALUES (18, 'idl_flows', null, null, 'dm_pik_fd_common/rdv_idl/flow_dumps');
INSERT INTO dict.dict_code_folder (code_folder_rk, code_folder_cd, code_folder_name, code_folder_desc, code_folder_path) VALUES (20, 'idl_flows', null, null, 'dm_pik_fd_dmfm/rdv_idl/flow_dumps');
INSERT INTO dict.dict_code_folder (code_folder_rk, code_folder_cd, code_folder_name, code_folder_desc, code_folder_path) VALUES (22, 'idl_flows', null, null, 'dm_pik_fd_dpbr/rdv_idl/flow_dumps');
INSERT INTO dict.dict_code_folder (code_folder_rk, code_folder_cd, code_folder_name, code_folder_desc, code_folder_path) VALUES (24, 'idl_flows', null, null, 'dm_pik_fd_dpmr/rdv_idl/flow_dumps');
INSERT INTO dict.dict_code_folder (code_folder_rk, code_folder_cd, code_folder_name, code_folder_desc, code_folder_path) VALUES (26, 'idl_flows', null, null, 'dm_pik_fd_dmor/rdv_idl/flow_dumps');
INSERT INTO dict.dict_code_folder (code_folder_rk, code_folder_cd, code_folder_name, code_folder_desc, code_folder_path) VALUES (28, 'idl_flows', null, null, 'dm_pik_fd_dmfr/rdv_idl/flow_dumps');
INSERT INTO dict.dict_code_folder (code_folder_rk, code_folder_cd, code_folder_name, code_folder_desc, code_folder_path) VALUES (30, 'idl_flows', null, null, 'dm_pik_fd_olap/rdv_idl/flow_dumps');
INSERT INTO dict.dict_code_folder (code_folder_rk, code_folder_cd, code_folder_name, code_folder_desc, code_folder_path) VALUES (32, 'idl_flows', null, null, 'dm_pik_fd_dkpi/rdv_idl/flow_dumps');
INSERT INTO dict.dict_code_folder (code_folder_rk, code_folder_cd, code_folder_name, code_folder_desc, code_folder_path) VALUES (34, 'idl_flows', null, null, 'dm_t_dtros_dtpl/rdv_idl/flow_dumps');
INSERT INTO dict.dict_code_folder (code_folder_rk, code_folder_cd, code_folder_name, code_folder_desc, code_folder_path) VALUES (36, 'idl_flows', null, null, 'dm_t_dkkr_pflmb/rdv_idl/flow_dumps');
INSERT INTO dict.dict_code_folder (code_folder_rk, code_folder_cd, code_folder_name, code_folder_desc, code_folder_path) VALUES (38, 'idl_flows', null, null, 'dm_t_diur_skmb/rdv_idl/flow_dumps');
INSERT INTO dict.dict_code_folder (code_folder_rk, code_folder_cd, code_folder_name, code_folder_desc, code_folder_path) VALUES (40, 'idl_flows', null, null, 'dm_t_drkr_defaults/rdv_idl/flow_dumps');
INSERT INTO dict.dict_code_folder (code_folder_rk, code_folder_cd, code_folder_name, code_folder_desc, code_folder_path) VALUES (42, 'idl_flows', null, null, 'dm_t_drkr_dmrr/rdv_idl/flow_dumps');
INSERT INTO dict.dict_code_folder (code_folder_rk, code_folder_cd, code_folder_name, code_folder_desc, code_folder_path) VALUES (44, 'idl_flows', null, null, 'dm_t_drkr_dmds/rdv_idl/flow_dumps');
INSERT INTO dict.dict_code_folder (code_folder_rk, code_folder_cd, code_folder_name, code_folder_desc, code_folder_path) VALUES (46, 'idl_flows', null, null, 'dm_t_drkr_rep/rdv_idl/flow_dumps');
INSERT INTO dict.dict_code_folder (code_folder_rk, code_folder_cd, code_folder_name, code_folder_desc, code_folder_path) VALUES (48, 'idl_flows', null, null, 'dm_t_drkr_drkb/rdv_idl/flow_dumps');
INSERT INTO dict.dict_code_folder (code_folder_rk, code_folder_cd, code_folder_name, code_folder_desc, code_folder_path) VALUES (50, 'idl_flows', null, null, 'dm_pik_fd_cagm/rdv_idl/flow_dumps');
INSERT INTO dict.dict_code_folder (code_folder_rk, code_folder_cd, code_folder_name, code_folder_desc, code_folder_path) VALUES (52, 'idl_flows', null, null, 'dm_rawd/rdv_idl/flow_dumps');
INSERT INTO dict.dict_code_folder (code_folder_rk, code_folder_cd, code_folder_name, code_folder_desc, code_folder_path) VALUES (54, 'idl_flows', null, null, 'dm_mhed/rdv_idl/flow_dumps');
INSERT INTO dict.dict_code_folder (code_folder_rk, code_folder_cd, code_folder_name, code_folder_desc, code_folder_path) VALUES (56, 'idl_flows', null, null, 'dm_str_its76_radar/rdv_idl/flow_dumps');
INSERT INTO dict.dict_code_folder (code_folder_rk, code_folder_cd, code_folder_name, code_folder_desc, code_folder_path) VALUES (58, 'idl_flows', null, null, 'dm_str_its76_armsed/rdv_idl/flow_dumps');
INSERT INTO dict.dict_code_folder (code_folder_rk, code_folder_cd, code_folder_name, code_folder_desc, code_folder_path) VALUES (60, 'idl_flows', null, null, 'dm_str_its76_brdro/rdv_idl/flow_dumps');
INSERT INTO dict.dict_code_folder (code_folder_rk, code_folder_cd, code_folder_name, code_folder_desc, code_folder_path) VALUES (62, 'idl_flows', null, null, 'dm_str_its76_endw/rdv_idl/flow_dumps');
INSERT INTO dict.dict_code_folder (code_folder_rk, code_folder_cd, code_folder_name, code_folder_desc, code_folder_path) VALUES (64, 'idl_flows', null, null, 'dm_str_its76_gosref/rdv_idl/flow_dumps');
INSERT INTO dict.dict_code_folder (code_folder_rk, code_folder_cd, code_folder_name, code_folder_desc, code_folder_path) VALUES (66, 'idl_flows', null, null, 'dm_str_its76_custbvlm/rdv_idl/flow_dumps');
INSERT INTO dict.dict_code_folder (code_folder_rk, code_folder_cd, code_folder_name, code_folder_desc, code_folder_path) VALUES (68, 'idl_flows', null, null, 'dm_pik_duio_rwa/rdv_idl/flow_dumps');
INSERT INTO dict.dict_code_folder (code_folder_rk, code_folder_cd, code_folder_name, code_folder_desc, code_folder_path) VALUES (70, 'idl_flows', null, null, 'dm_t_diur_irb/rdv_idl/flow_dumps');
INSERT INTO dict.dict_code_folder (code_folder_rk, code_folder_cd, code_folder_name, code_folder_desc, code_folder_path) VALUES (72, 'idl_flows', null, null, 'dm_pik_duio_std/rdv_idl/flow_dumps');
INSERT INTO dict.dict_code_folder (code_folder_rk, code_folder_cd, code_folder_name, code_folder_desc, code_folder_path) VALUES (74, 'idl_flows', null, null, 'dm_rb_crm_dmpf/rdv_idl/flow_dumps');
INSERT INTO dict.dict_code_folder (code_folder_rk, code_folder_cd, code_folder_name, code_folder_desc, code_folder_path) VALUES (76, 'idl_flows', null, null, 'dm_t_drkr_rkk/rdv_idl/flow_dumps');
INSERT INTO dict.dict_code_folder (code_folder_rk, code_folder_cd, code_folder_name, code_folder_desc, code_folder_path) VALUES (78, 'idl_flows', null, null, 'dm_str_its76_analitvit/rdv_idl/flow_dumps');
INSERT INTO dict.dict_code_folder (code_folder_rk, code_folder_cd, code_folder_name, code_folder_desc, code_folder_path) VALUES (80, 'idl_flows', null, null, 'dm_str_its76_segment/rdv_idl/flow_dumps');
INSERT INTO dict.dict_code_folder (code_folder_rk, code_folder_cd, code_folder_name, code_folder_desc, code_folder_path) VALUES (82, 'idl_flows', null, null, 'dm_spo/rdv_idl/flow_dumps');
INSERT INTO dict.dict_code_folder (code_folder_rk, code_folder_cd, code_folder_name, code_folder_desc, code_folder_path) VALUES (84, 'idl_flows', null, null, 'dm_t_dopb_3462u/rdv_idl/flow_dumps');
INSERT INTO dict.dict_code_folder (code_folder_rk, code_folder_cd, code_folder_name, code_folder_desc, code_folder_path) VALUES (86, 'idl_flows', null, null, 'dm_t_drkr_sdm/rdv_idl/flow_dumps');
INSERT INTO dict.dict_code_folder (code_folder_rk, code_folder_cd, code_folder_name, code_folder_desc, code_folder_path) VALUES (88, 'idl_flows', null, null, 'dm_t_drkr_rrdm/rdv_idl/flow_dumps');
INSERT INTO dict.dict_code_folder (code_folder_rk, code_folder_cd, code_folder_name, code_folder_desc, code_folder_path) VALUES (90, 'idl_flows', null, null, 'dm_pik_dob_spmd/rdv_idl/flow_dumps');
INSERT INTO dict.dict_code_folder (code_folder_rk, code_folder_cd, code_folder_name, code_folder_desc, code_folder_path) VALUES (92, 'idl_flows', null, null, 'dm_pik_fd_bfvc/rdv_idl/flow_dumps');
INSERT INTO dict.dict_code_folder (code_folder_rk, code_folder_cd, code_folder_name, code_folder_desc, code_folder_path) VALUES (94, 'idl_flows', null, null, 'dm_pik_fd_sros/rdv_idl/flow_dumps');
INSERT INTO dict.dict_code_folder (code_folder_rk, code_folder_cd, code_folder_name, code_folder_desc, code_folder_path) VALUES (96, 'idl_flows', null, null, 'rd_khd/rdv_idl/flow_dumps');
INSERT INTO dict.dict_code_folder (code_folder_rk, code_folder_cd, code_folder_name, code_folder_desc, code_folder_path) VALUES (98, 'idl_flows', null, null, 'rd_khd_dds/rdv_idl/flow_dumps');
INSERT INTO dict.dict_code_folder (code_folder_rk, code_folder_cd, code_folder_name, code_folder_desc, code_folder_path) VALUES (100, 'idl_flows', null, null, 'dm_common/rdv_idl/flow_dumps');
INSERT INTO dict.dict_code_folder (code_folder_rk, code_folder_cd, code_folder_name, code_folder_desc, code_folder_path) VALUES (102, 'idl_flows', null, null, 'dm_cdm_lvl1/rdv_idl/flow_dumps');
INSERT INTO dict.dict_code_folder (code_folder_rk, code_folder_cd, code_folder_name, code_folder_desc, code_folder_path) VALUES (104, 'idl_flows', null, null, 'dm_cdm_hist/rdv_idl/flow_dumps');
INSERT INTO dict.dict_code_folder (code_folder_rk, code_folder_cd, code_folder_name, code_folder_desc, code_folder_path) VALUES (106, 'idl_flows', null, null, 'dm_drev/rdv_idl/flow_dumps');

CREATE TABLE dict.link_code_delivery_folder (
    code_delivery_rk    SMALLINT    NOT NULL,
    code_folder_rk      SMALLINT    NOT NULL,
    is_required_flg     BOOLEAN     NOT NULL,
    is_flow_flg         BOOLEAN     NOT NULL,
    is_master_flow_flg  BOOLEAN     NOT NULL,
    is_ddl_flg          BOOLEAN     NOT NULL,
    is_resource_flg     BOOLEAN     NOT NULL,
    CONSTRAINT link_code_delivery_folder_pk
        PRIMARY KEY (code_delivery_rk, code_folder_rk)
);
CREATE INDEX link_flow_source_idx
          ON dict.link_code_delivery_folder (code_delivery_rk);
create index link_flow_target_idx
          ON dict.link_code_delivery_folder (code_folder_rk);
INSERT INTO dict.link_code_delivery_folder (code_delivery_rk, code_folder_rk, is_required_flg, is_flow_flg, is_master_flow_flg, is_ddl_flg, is_resource_flg) VALUES (155, 1, true, true, false, false, false);
INSERT INTO dict.link_code_delivery_folder (code_delivery_rk, code_folder_rk, is_required_flg, is_flow_flg, is_master_flow_flg, is_ddl_flg, is_resource_flg) VALUES (155, 2, true, true, false, false, false);
INSERT INTO dict.link_code_delivery_folder (code_delivery_rk, code_folder_rk, is_required_flg, is_flow_flg, is_master_flow_flg, is_ddl_flg, is_resource_flg) VALUES (156, 3, true, true, false, false, false);
INSERT INTO dict.link_code_delivery_folder (code_delivery_rk, code_folder_rk, is_required_flg, is_flow_flg, is_master_flow_flg, is_ddl_flg, is_resource_flg) VALUES (156, 4, true, true, false, false, false);
INSERT INTO dict.link_code_delivery_folder (code_delivery_rk, code_folder_rk, is_required_flg, is_flow_flg, is_master_flow_flg, is_ddl_flg, is_resource_flg) VALUES (157, 5, true, true, false, false, false);
INSERT INTO dict.link_code_delivery_folder (code_delivery_rk, code_folder_rk, is_required_flg, is_flow_flg, is_master_flow_flg, is_ddl_flg, is_resource_flg) VALUES (157, 6, true, true, false, false, false);
INSERT INTO dict.link_code_delivery_folder (code_delivery_rk, code_folder_rk, is_required_flg, is_flow_flg, is_master_flow_flg, is_ddl_flg, is_resource_flg) VALUES (158, 7, true, true, false, false, false);
INSERT INTO dict.link_code_delivery_folder (code_delivery_rk, code_folder_rk, is_required_flg, is_flow_flg, is_master_flow_flg, is_ddl_flg, is_resource_flg) VALUES (158, 8, true, true, false, false, false);
INSERT INTO dict.link_code_delivery_folder (code_delivery_rk, code_folder_rk, is_required_flg, is_flow_flg, is_master_flow_flg, is_ddl_flg, is_resource_flg) VALUES (159, 9, true, true, false, false, false);
INSERT INTO dict.link_code_delivery_folder (code_delivery_rk, code_folder_rk, is_required_flg, is_flow_flg, is_master_flow_flg, is_ddl_flg, is_resource_flg) VALUES (159, 10, true, true, false, false, false);
INSERT INTO dict.link_code_delivery_folder (code_delivery_rk, code_folder_rk, is_required_flg, is_flow_flg, is_master_flow_flg, is_ddl_flg, is_resource_flg) VALUES (160, 11, true, true, false, false, false);
INSERT INTO dict.link_code_delivery_folder (code_delivery_rk, code_folder_rk, is_required_flg, is_flow_flg, is_master_flow_flg, is_ddl_flg, is_resource_flg) VALUES (160, 12, true, true, false, false, false);
INSERT INTO dict.link_code_delivery_folder (code_delivery_rk, code_folder_rk, is_required_flg, is_flow_flg, is_master_flow_flg, is_ddl_flg, is_resource_flg) VALUES (161, 13, true, true, false, false, false);
INSERT INTO dict.link_code_delivery_folder (code_delivery_rk, code_folder_rk, is_required_flg, is_flow_flg, is_master_flow_flg, is_ddl_flg, is_resource_flg) VALUES (161, 14, true, true, false, false, false);
INSERT INTO dict.link_code_delivery_folder (code_delivery_rk, code_folder_rk, is_required_flg, is_flow_flg, is_master_flow_flg, is_ddl_flg, is_resource_flg) VALUES (162, 15, true, true, false, false, false);
INSERT INTO dict.link_code_delivery_folder (code_delivery_rk, code_folder_rk, is_required_flg, is_flow_flg, is_master_flow_flg, is_ddl_flg, is_resource_flg) VALUES (162, 16, true, true, false, false, false);
INSERT INTO dict.link_code_delivery_folder (code_delivery_rk, code_folder_rk, is_required_flg, is_flow_flg, is_master_flow_flg, is_ddl_flg, is_resource_flg) VALUES (163, 17, true, true, false, false, false);
INSERT INTO dict.link_code_delivery_folder (code_delivery_rk, code_folder_rk, is_required_flg, is_flow_flg, is_master_flow_flg, is_ddl_flg, is_resource_flg) VALUES (163, 18, true, true, false, false, false);
INSERT INTO dict.link_code_delivery_folder (code_delivery_rk, code_folder_rk, is_required_flg, is_flow_flg, is_master_flow_flg, is_ddl_flg, is_resource_flg) VALUES (164, 19, true, true, false, false, false);
INSERT INTO dict.link_code_delivery_folder (code_delivery_rk, code_folder_rk, is_required_flg, is_flow_flg, is_master_flow_flg, is_ddl_flg, is_resource_flg) VALUES (164, 20, true, true, false, false, false);
INSERT INTO dict.link_code_delivery_folder (code_delivery_rk, code_folder_rk, is_required_flg, is_flow_flg, is_master_flow_flg, is_ddl_flg, is_resource_flg) VALUES (165, 21, true, true, false, false, false);
INSERT INTO dict.link_code_delivery_folder (code_delivery_rk, code_folder_rk, is_required_flg, is_flow_flg, is_master_flow_flg, is_ddl_flg, is_resource_flg) VALUES (165, 22, true, true, false, false, false);
INSERT INTO dict.link_code_delivery_folder (code_delivery_rk, code_folder_rk, is_required_flg, is_flow_flg, is_master_flow_flg, is_ddl_flg, is_resource_flg) VALUES (166, 23, true, true, false, false, false);
INSERT INTO dict.link_code_delivery_folder (code_delivery_rk, code_folder_rk, is_required_flg, is_flow_flg, is_master_flow_flg, is_ddl_flg, is_resource_flg) VALUES (166, 24, true, true, false, false, false);
INSERT INTO dict.link_code_delivery_folder (code_delivery_rk, code_folder_rk, is_required_flg, is_flow_flg, is_master_flow_flg, is_ddl_flg, is_resource_flg) VALUES (167, 25, true, true, false, false, false);
INSERT INTO dict.link_code_delivery_folder (code_delivery_rk, code_folder_rk, is_required_flg, is_flow_flg, is_master_flow_flg, is_ddl_flg, is_resource_flg) VALUES (167, 26, true, true, false, false, false);
INSERT INTO dict.link_code_delivery_folder (code_delivery_rk, code_folder_rk, is_required_flg, is_flow_flg, is_master_flow_flg, is_ddl_flg, is_resource_flg) VALUES (168, 27, true, true, false, false, false);
INSERT INTO dict.link_code_delivery_folder (code_delivery_rk, code_folder_rk, is_required_flg, is_flow_flg, is_master_flow_flg, is_ddl_flg, is_resource_flg) VALUES (168, 28, true, true, false, false, false);
INSERT INTO dict.link_code_delivery_folder (code_delivery_rk, code_folder_rk, is_required_flg, is_flow_flg, is_master_flow_flg, is_ddl_flg, is_resource_flg) VALUES (169, 29, true, true, false, false, false);
INSERT INTO dict.link_code_delivery_folder (code_delivery_rk, code_folder_rk, is_required_flg, is_flow_flg, is_master_flow_flg, is_ddl_flg, is_resource_flg) VALUES (169, 30, true, true, false, false, false);
INSERT INTO dict.link_code_delivery_folder (code_delivery_rk, code_folder_rk, is_required_flg, is_flow_flg, is_master_flow_flg, is_ddl_flg, is_resource_flg) VALUES (170, 31, true, true, false, false, false);
INSERT INTO dict.link_code_delivery_folder (code_delivery_rk, code_folder_rk, is_required_flg, is_flow_flg, is_master_flow_flg, is_ddl_flg, is_resource_flg) VALUES (170, 32, true, true, false, false, false);
INSERT INTO dict.link_code_delivery_folder (code_delivery_rk, code_folder_rk, is_required_flg, is_flow_flg, is_master_flow_flg, is_ddl_flg, is_resource_flg) VALUES (171, 33, true, true, false, false, false);
INSERT INTO dict.link_code_delivery_folder (code_delivery_rk, code_folder_rk, is_required_flg, is_flow_flg, is_master_flow_flg, is_ddl_flg, is_resource_flg) VALUES (171, 34, true, true, false, false, false);
INSERT INTO dict.link_code_delivery_folder (code_delivery_rk, code_folder_rk, is_required_flg, is_flow_flg, is_master_flow_flg, is_ddl_flg, is_resource_flg) VALUES (172, 35, true, true, false, false, false);
INSERT INTO dict.link_code_delivery_folder (code_delivery_rk, code_folder_rk, is_required_flg, is_flow_flg, is_master_flow_flg, is_ddl_flg, is_resource_flg) VALUES (172, 36, true, true, false, false, false);
INSERT INTO dict.link_code_delivery_folder (code_delivery_rk, code_folder_rk, is_required_flg, is_flow_flg, is_master_flow_flg, is_ddl_flg, is_resource_flg) VALUES (173, 37, true, true, false, false, false);
INSERT INTO dict.link_code_delivery_folder (code_delivery_rk, code_folder_rk, is_required_flg, is_flow_flg, is_master_flow_flg, is_ddl_flg, is_resource_flg) VALUES (173, 38, true, true, false, false, false);
INSERT INTO dict.link_code_delivery_folder (code_delivery_rk, code_folder_rk, is_required_flg, is_flow_flg, is_master_flow_flg, is_ddl_flg, is_resource_flg) VALUES (174, 39, true, true, false, false, false);
INSERT INTO dict.link_code_delivery_folder (code_delivery_rk, code_folder_rk, is_required_flg, is_flow_flg, is_master_flow_flg, is_ddl_flg, is_resource_flg) VALUES (174, 40, true, true, false, false, false);
INSERT INTO dict.link_code_delivery_folder (code_delivery_rk, code_folder_rk, is_required_flg, is_flow_flg, is_master_flow_flg, is_ddl_flg, is_resource_flg) VALUES (175, 41, true, true, false, false, false);
INSERT INTO dict.link_code_delivery_folder (code_delivery_rk, code_folder_rk, is_required_flg, is_flow_flg, is_master_flow_flg, is_ddl_flg, is_resource_flg) VALUES (175, 42, true, true, false, false, false);
INSERT INTO dict.link_code_delivery_folder (code_delivery_rk, code_folder_rk, is_required_flg, is_flow_flg, is_master_flow_flg, is_ddl_flg, is_resource_flg) VALUES (176, 43, true, true, false, false, false);
INSERT INTO dict.link_code_delivery_folder (code_delivery_rk, code_folder_rk, is_required_flg, is_flow_flg, is_master_flow_flg, is_ddl_flg, is_resource_flg) VALUES (176, 44, true, true, false, false, false);
INSERT INTO dict.link_code_delivery_folder (code_delivery_rk, code_folder_rk, is_required_flg, is_flow_flg, is_master_flow_flg, is_ddl_flg, is_resource_flg) VALUES (177, 45, true, true, false, false, false);
INSERT INTO dict.link_code_delivery_folder (code_delivery_rk, code_folder_rk, is_required_flg, is_flow_flg, is_master_flow_flg, is_ddl_flg, is_resource_flg) VALUES (177, 46, true, true, false, false, false);
INSERT INTO dict.link_code_delivery_folder (code_delivery_rk, code_folder_rk, is_required_flg, is_flow_flg, is_master_flow_flg, is_ddl_flg, is_resource_flg) VALUES (178, 47, true, true, false, false, false);
INSERT INTO dict.link_code_delivery_folder (code_delivery_rk, code_folder_rk, is_required_flg, is_flow_flg, is_master_flow_flg, is_ddl_flg, is_resource_flg) VALUES (178, 48, true, true, false, false, false);
INSERT INTO dict.link_code_delivery_folder (code_delivery_rk, code_folder_rk, is_required_flg, is_flow_flg, is_master_flow_flg, is_ddl_flg, is_resource_flg) VALUES (179, 49, true, true, false, false, false);
INSERT INTO dict.link_code_delivery_folder (code_delivery_rk, code_folder_rk, is_required_flg, is_flow_flg, is_master_flow_flg, is_ddl_flg, is_resource_flg) VALUES (179, 50, true, true, false, false, false);
INSERT INTO dict.link_code_delivery_folder (code_delivery_rk, code_folder_rk, is_required_flg, is_flow_flg, is_master_flow_flg, is_ddl_flg, is_resource_flg) VALUES (180, 51, true, true, false, false, false);
INSERT INTO dict.link_code_delivery_folder (code_delivery_rk, code_folder_rk, is_required_flg, is_flow_flg, is_master_flow_flg, is_ddl_flg, is_resource_flg) VALUES (180, 52, true, true, false, false, false);
INSERT INTO dict.link_code_delivery_folder (code_delivery_rk, code_folder_rk, is_required_flg, is_flow_flg, is_master_flow_flg, is_ddl_flg, is_resource_flg) VALUES (181, 53, true, true, false, false, false);
INSERT INTO dict.link_code_delivery_folder (code_delivery_rk, code_folder_rk, is_required_flg, is_flow_flg, is_master_flow_flg, is_ddl_flg, is_resource_flg) VALUES (181, 54, true, true, false, false, false);
INSERT INTO dict.link_code_delivery_folder (code_delivery_rk, code_folder_rk, is_required_flg, is_flow_flg, is_master_flow_flg, is_ddl_flg, is_resource_flg) VALUES (182, 55, true, true, false, false, false);
INSERT INTO dict.link_code_delivery_folder (code_delivery_rk, code_folder_rk, is_required_flg, is_flow_flg, is_master_flow_flg, is_ddl_flg, is_resource_flg) VALUES (182, 56, true, true, false, false, false);
INSERT INTO dict.link_code_delivery_folder (code_delivery_rk, code_folder_rk, is_required_flg, is_flow_flg, is_master_flow_flg, is_ddl_flg, is_resource_flg) VALUES (183, 57, true, true, false, false, false);
INSERT INTO dict.link_code_delivery_folder (code_delivery_rk, code_folder_rk, is_required_flg, is_flow_flg, is_master_flow_flg, is_ddl_flg, is_resource_flg) VALUES (183, 58, true, true, false, false, false);
INSERT INTO dict.link_code_delivery_folder (code_delivery_rk, code_folder_rk, is_required_flg, is_flow_flg, is_master_flow_flg, is_ddl_flg, is_resource_flg) VALUES (184, 59, true, true, false, false, false);
INSERT INTO dict.link_code_delivery_folder (code_delivery_rk, code_folder_rk, is_required_flg, is_flow_flg, is_master_flow_flg, is_ddl_flg, is_resource_flg) VALUES (184, 60, true, true, false, false, false);
INSERT INTO dict.link_code_delivery_folder (code_delivery_rk, code_folder_rk, is_required_flg, is_flow_flg, is_master_flow_flg, is_ddl_flg, is_resource_flg) VALUES (185, 61, true, true, false, false, false);
INSERT INTO dict.link_code_delivery_folder (code_delivery_rk, code_folder_rk, is_required_flg, is_flow_flg, is_master_flow_flg, is_ddl_flg, is_resource_flg) VALUES (185, 62, true, true, false, false, false);
INSERT INTO dict.link_code_delivery_folder (code_delivery_rk, code_folder_rk, is_required_flg, is_flow_flg, is_master_flow_flg, is_ddl_flg, is_resource_flg) VALUES (186, 63, true, true, false, false, false);
INSERT INTO dict.link_code_delivery_folder (code_delivery_rk, code_folder_rk, is_required_flg, is_flow_flg, is_master_flow_flg, is_ddl_flg, is_resource_flg) VALUES (186, 64, true, true, false, false, false);
INSERT INTO dict.link_code_delivery_folder (code_delivery_rk, code_folder_rk, is_required_flg, is_flow_flg, is_master_flow_flg, is_ddl_flg, is_resource_flg) VALUES (187, 65, true, true, false, false, false);
INSERT INTO dict.link_code_delivery_folder (code_delivery_rk, code_folder_rk, is_required_flg, is_flow_flg, is_master_flow_flg, is_ddl_flg, is_resource_flg) VALUES (187, 66, true, true, false, false, false);
INSERT INTO dict.link_code_delivery_folder (code_delivery_rk, code_folder_rk, is_required_flg, is_flow_flg, is_master_flow_flg, is_ddl_flg, is_resource_flg) VALUES (188, 67, true, true, false, false, false);
INSERT INTO dict.link_code_delivery_folder (code_delivery_rk, code_folder_rk, is_required_flg, is_flow_flg, is_master_flow_flg, is_ddl_flg, is_resource_flg) VALUES (188, 68, true, true, false, false, false);
INSERT INTO dict.link_code_delivery_folder (code_delivery_rk, code_folder_rk, is_required_flg, is_flow_flg, is_master_flow_flg, is_ddl_flg, is_resource_flg) VALUES (189, 69, true, true, false, false, false);
INSERT INTO dict.link_code_delivery_folder (code_delivery_rk, code_folder_rk, is_required_flg, is_flow_flg, is_master_flow_flg, is_ddl_flg, is_resource_flg) VALUES (189, 70, true, true, false, false, false);
INSERT INTO dict.link_code_delivery_folder (code_delivery_rk, code_folder_rk, is_required_flg, is_flow_flg, is_master_flow_flg, is_ddl_flg, is_resource_flg) VALUES (190, 71, true, true, false, false, false);
INSERT INTO dict.link_code_delivery_folder (code_delivery_rk, code_folder_rk, is_required_flg, is_flow_flg, is_master_flow_flg, is_ddl_flg, is_resource_flg) VALUES (190, 72, true, true, false, false, false);
INSERT INTO dict.link_code_delivery_folder (code_delivery_rk, code_folder_rk, is_required_flg, is_flow_flg, is_master_flow_flg, is_ddl_flg, is_resource_flg) VALUES (191, 73, true, true, false, false, false);
INSERT INTO dict.link_code_delivery_folder (code_delivery_rk, code_folder_rk, is_required_flg, is_flow_flg, is_master_flow_flg, is_ddl_flg, is_resource_flg) VALUES (191, 74, true, true, false, false, false);
INSERT INTO dict.link_code_delivery_folder (code_delivery_rk, code_folder_rk, is_required_flg, is_flow_flg, is_master_flow_flg, is_ddl_flg, is_resource_flg) VALUES (192, 75, true, true, false, false, false);
INSERT INTO dict.link_code_delivery_folder (code_delivery_rk, code_folder_rk, is_required_flg, is_flow_flg, is_master_flow_flg, is_ddl_flg, is_resource_flg) VALUES (192, 76, true, true, false, false, false);
INSERT INTO dict.link_code_delivery_folder (code_delivery_rk, code_folder_rk, is_required_flg, is_flow_flg, is_master_flow_flg, is_ddl_flg, is_resource_flg) VALUES (193, 77, true, true, false, false, false);
INSERT INTO dict.link_code_delivery_folder (code_delivery_rk, code_folder_rk, is_required_flg, is_flow_flg, is_master_flow_flg, is_ddl_flg, is_resource_flg) VALUES (193, 78, true, true, false, false, false);
INSERT INTO dict.link_code_delivery_folder (code_delivery_rk, code_folder_rk, is_required_flg, is_flow_flg, is_master_flow_flg, is_ddl_flg, is_resource_flg) VALUES (194, 79, true, true, false, false, false);
INSERT INTO dict.link_code_delivery_folder (code_delivery_rk, code_folder_rk, is_required_flg, is_flow_flg, is_master_flow_flg, is_ddl_flg, is_resource_flg) VALUES (194, 80, true, true, false, false, false);
INSERT INTO dict.link_code_delivery_folder (code_delivery_rk, code_folder_rk, is_required_flg, is_flow_flg, is_master_flow_flg, is_ddl_flg, is_resource_flg) VALUES (195, 81, true, true, false, false, false);
INSERT INTO dict.link_code_delivery_folder (code_delivery_rk, code_folder_rk, is_required_flg, is_flow_flg, is_master_flow_flg, is_ddl_flg, is_resource_flg) VALUES (195, 82, true, true, false, false, false);
INSERT INTO dict.link_code_delivery_folder (code_delivery_rk, code_folder_rk, is_required_flg, is_flow_flg, is_master_flow_flg, is_ddl_flg, is_resource_flg) VALUES (196, 83, true, true, false, false, false);
INSERT INTO dict.link_code_delivery_folder (code_delivery_rk, code_folder_rk, is_required_flg, is_flow_flg, is_master_flow_flg, is_ddl_flg, is_resource_flg) VALUES (196, 84, true, true, false, false, false);
INSERT INTO dict.link_code_delivery_folder (code_delivery_rk, code_folder_rk, is_required_flg, is_flow_flg, is_master_flow_flg, is_ddl_flg, is_resource_flg) VALUES (197, 85, true, true, false, false, false);
INSERT INTO dict.link_code_delivery_folder (code_delivery_rk, code_folder_rk, is_required_flg, is_flow_flg, is_master_flow_flg, is_ddl_flg, is_resource_flg) VALUES (197, 86, true, true, false, false, false);
INSERT INTO dict.link_code_delivery_folder (code_delivery_rk, code_folder_rk, is_required_flg, is_flow_flg, is_master_flow_flg, is_ddl_flg, is_resource_flg) VALUES (198, 87, true, true, false, false, false);
INSERT INTO dict.link_code_delivery_folder (code_delivery_rk, code_folder_rk, is_required_flg, is_flow_flg, is_master_flow_flg, is_ddl_flg, is_resource_flg) VALUES (198, 88, true, true, false, false, false);
INSERT INTO dict.link_code_delivery_folder (code_delivery_rk, code_folder_rk, is_required_flg, is_flow_flg, is_master_flow_flg, is_ddl_flg, is_resource_flg) VALUES (199, 89, true, true, false, false, false);
INSERT INTO dict.link_code_delivery_folder (code_delivery_rk, code_folder_rk, is_required_flg, is_flow_flg, is_master_flow_flg, is_ddl_flg, is_resource_flg) VALUES (199, 90, true, true, false, false, false);
INSERT INTO dict.link_code_delivery_folder (code_delivery_rk, code_folder_rk, is_required_flg, is_flow_flg, is_master_flow_flg, is_ddl_flg, is_resource_flg) VALUES (200, 91, true, true, false, false, false);
INSERT INTO dict.link_code_delivery_folder (code_delivery_rk, code_folder_rk, is_required_flg, is_flow_flg, is_master_flow_flg, is_ddl_flg, is_resource_flg) VALUES (200, 92, true, true, false, false, false);
INSERT INTO dict.link_code_delivery_folder (code_delivery_rk, code_folder_rk, is_required_flg, is_flow_flg, is_master_flow_flg, is_ddl_flg, is_resource_flg) VALUES (201, 93, true, true, false, false, false);
INSERT INTO dict.link_code_delivery_folder (code_delivery_rk, code_folder_rk, is_required_flg, is_flow_flg, is_master_flow_flg, is_ddl_flg, is_resource_flg) VALUES (201, 94, true, true, false, false, false);
INSERT INTO dict.link_code_delivery_folder (code_delivery_rk, code_folder_rk, is_required_flg, is_flow_flg, is_master_flow_flg, is_ddl_flg, is_resource_flg) VALUES (208, 95, true, true, false, false, false);
INSERT INTO dict.link_code_delivery_folder (code_delivery_rk, code_folder_rk, is_required_flg, is_flow_flg, is_master_flow_flg, is_ddl_flg, is_resource_flg) VALUES (208, 96, true, true, false, false, false);
INSERT INTO dict.link_code_delivery_folder (code_delivery_rk, code_folder_rk, is_required_flg, is_flow_flg, is_master_flow_flg, is_ddl_flg, is_resource_flg) VALUES (209, 97, true, true, false, false, false);
INSERT INTO dict.link_code_delivery_folder (code_delivery_rk, code_folder_rk, is_required_flg, is_flow_flg, is_master_flow_flg, is_ddl_flg, is_resource_flg) VALUES (209, 98, true, true, false, false, false);
INSERT INTO dict.link_code_delivery_folder (code_delivery_rk, code_folder_rk, is_required_flg, is_flow_flg, is_master_flow_flg, is_ddl_flg, is_resource_flg) VALUES (210, 99, true, true, false, false, false);
INSERT INTO dict.link_code_delivery_folder (code_delivery_rk, code_folder_rk, is_required_flg, is_flow_flg, is_master_flow_flg, is_ddl_flg, is_resource_flg) VALUES (210, 100, true, true, false, false, false);
INSERT INTO dict.link_code_delivery_folder (code_delivery_rk, code_folder_rk, is_required_flg, is_flow_flg, is_master_flow_flg, is_ddl_flg, is_resource_flg) VALUES (211, 101, true, true, false, false, false);
INSERT INTO dict.link_code_delivery_folder (code_delivery_rk, code_folder_rk, is_required_flg, is_flow_flg, is_master_flow_flg, is_ddl_flg, is_resource_flg) VALUES (211, 102, true, true, false, false, false);
INSERT INTO dict.link_code_delivery_folder (code_delivery_rk, code_folder_rk, is_required_flg, is_flow_flg, is_master_flow_flg, is_ddl_flg, is_resource_flg) VALUES (212, 103, true, true, false, false, false);
INSERT INTO dict.link_code_delivery_folder (code_delivery_rk, code_folder_rk, is_required_flg, is_flow_flg, is_master_flow_flg, is_ddl_flg, is_resource_flg) VALUES (212, 104, true, true, false, false, false);
INSERT INTO dict.link_code_delivery_folder (code_delivery_rk, code_folder_rk, is_required_flg, is_flow_flg, is_master_flow_flg, is_ddl_flg, is_resource_flg) VALUES (213, 105, true, true, false, false, false);
INSERT INTO dict.link_code_delivery_folder (code_delivery_rk, code_folder_rk, is_required_flg, is_flow_flg, is_master_flow_flg, is_ddl_flg, is_resource_flg) VALUES (213, 106, true, true, false, false, false);


create table dict.dict_data_layer
(
    data_layer_rk   smallint   not null primary key,
    data_layer_cd   dict.dname not null,
    data_layer_name dict.dname not null,
    is_external_flg boolean    not null,
    is_rdv_flg      boolean    not null,
    is_stg_flg      boolean    not null,
    is_idl_flg      boolean    not null,
    is_bdm_flg      boolean    not null,
    is_dm_flg       boolean    not null,
    data_layer_desc text
);
insert into dict.dict_data_layer (data_layer_rk, data_layer_cd, data_layer_name, is_external_flg, is_rdv_flg, is_stg_flg, is_idl_flg, is_bdm_flg, is_dm_flg, data_layer_desc) values (-1, 'undef', 'undefined', false, false, false, false, false, false, 'Не определено');
insert into dict.dict_data_layer (data_layer_rk, data_layer_cd, data_layer_name, is_external_flg, is_rdv_flg, is_stg_flg, is_idl_flg, is_bdm_flg, is_dm_flg, data_layer_desc) values (1, 'rdv', 'rdv', false, true, false, false, false, false, 'Raw Data Vault');
insert into dict.dict_data_layer (data_layer_rk, data_layer_cd, data_layer_name, is_external_flg, is_rdv_flg, is_stg_flg, is_idl_flg, is_bdm_flg, is_dm_flg, data_layer_desc) values (2, 'stg', 'stg', false, false, true, false, false, false, 'Staging');
insert into dict.dict_data_layer (data_layer_rk, data_layer_cd, data_layer_name, is_external_flg, is_rdv_flg, is_stg_flg, is_idl_flg, is_bdm_flg, is_dm_flg, data_layer_desc) values (3, 'idl', 'idl', false, false, false, true, false, false, 'Integrated Data Layer');
insert into dict.dict_data_layer (data_layer_rk, data_layer_cd, data_layer_name, is_external_flg, is_rdv_flg, is_stg_flg, is_idl_flg, is_bdm_flg, is_dm_flg, data_layer_desc) values (4, 'bdm', 'bdm', false, false, false, false, true, false, 'Base Data Mart');
insert into dict.dict_data_layer (data_layer_rk, data_layer_cd, data_layer_name, is_external_flg, is_rdv_flg, is_stg_flg, is_idl_flg, is_bdm_flg, is_dm_flg, data_layer_desc) values (5, 'dm', 'dm', false, false, false, false, false, true, 'Data Mart');
insert into dict.dict_data_layer (data_layer_rk, data_layer_cd, data_layer_name, is_external_flg, is_rdv_flg, is_stg_flg, is_idl_flg, is_bdm_flg, is_dm_flg, data_layer_desc) values (6, 'ext', 'ext', true, false, false, false, false, false, 'External source');
insert into dict.dict_data_layer (data_layer_rk, data_layer_cd, data_layer_name, is_external_flg, is_rdv_flg, is_stg_flg, is_idl_flg, is_bdm_flg, is_dm_flg, data_layer_desc) values (7, 'ods', 'ods', true, false, false, false, false, false, 'Operational Data Storage');
insert into dict.dict_data_layer (data_layer_rk, data_layer_cd, data_layer_name, is_external_flg, is_rdv_flg, is_stg_flg, is_idl_flg, is_bdm_flg, is_dm_flg, data_layer_desc) values (8, 'cdm', 'cdm', false, false, false, false, false, true, 'Common Data Mart');
insert into dict.dict_data_layer (data_layer_rk, data_layer_cd, data_layer_name, is_external_flg, is_rdv_flg, is_stg_flg, is_idl_flg, is_bdm_flg, is_dm_flg, data_layer_desc) values (9, 'dapp', 'dapp', true, false, false, false, false, false, 'Data Analysis and Processing Platform');
insert into dict.dict_data_layer (data_layer_rk, data_layer_cd, data_layer_name, is_external_flg, is_rdv_flg, is_stg_flg, is_idl_flg, is_bdm_flg, is_dm_flg, data_layer_desc) values (10, 'dr', 'dr', true, false, false, false, false, false, 'Disaster Recovery');
insert into dict.dict_data_layer (data_layer_rk, data_layer_cd, data_layer_name, is_external_flg, is_rdv_flg, is_stg_flg, is_idl_flg, is_bdm_flg, is_dm_flg, data_layer_desc) values (11, 'drp', 'drp', true, false, false, false, false, false, 'Data Research Platform');
insert into dict.dict_data_layer (data_layer_rk, data_layer_cd, data_layer_name, is_external_flg, is_rdv_flg, is_stg_flg, is_idl_flg, is_bdm_flg, is_dm_flg, data_layer_desc) values (12, 'dds', 'dds', true, false, false, false, false, false, 'Data Detail Store');
insert into dict.dict_data_layer (data_layer_rk, data_layer_cd, data_layer_name, is_external_flg, is_rdv_flg, is_stg_flg, is_idl_flg, is_bdm_flg, is_dm_flg, data_layer_desc) values (13, 'sdm', 'sdm', false, false, false, false, false, true, 'Source Data Mart');


create table dict.dict_table_type
(
    table_type_rk   smallint   not null
        constraint dict_table_type_pk
            primary key,
    table_type_cd   dict.dname not null,
    table_type_name dict.dname not null,
    table_type_desc text
);
insert into dict.dict_table_type (table_type_rk, table_type_cd, table_type_name, table_type_desc) values (-1, 'undef', 'undefined', 'Не определено');
insert into dict.dict_table_type (table_type_rk, table_type_cd, table_type_name, table_type_desc) values (1, 'table', 'table', 'Таблица');
insert into dict.dict_table_type (table_type_rk, table_type_cd, table_type_name, table_type_desc) values (2, 'view', 'view', 'Представление');
insert into dict.dict_table_type (table_type_rk, table_type_cd, table_type_name, table_type_desc) values (3, 'function', 'function', 'Функция');
insert into dict.dict_table_type (table_type_rk, table_type_cd, table_type_name, table_type_desc) values (4, 'structured object', 'structured object', 'Структурированный объект');


create table dict.dict_table_distribution_type
(
    table_distribution_type_rk   smallint   not null
        constraint dict_table_distribution_type_pk
            primary key,
    table_distribution_type_cd   dict.dname not null,
    table_distribution_type_name dict.dname not null,
    table_distribution_type_desc text
);
insert into dict.dict_table_distribution_type (table_distribution_type_rk, table_distribution_type_cd, table_distribution_type_name, table_distribution_type_desc) values (-1, 'undef', 'undefined', 'Не определено');
insert into dict.dict_table_distribution_type (table_distribution_type_rk, table_distribution_type_cd, table_distribution_type_name, table_distribution_type_desc) values (1, 'partitioned', 'partitioned', 'partitioned');
insert into dict.dict_table_distribution_type (table_distribution_type_rk, table_distribution_type_cd, table_distribution_type_name, table_distribution_type_desc) values (2, 'replicated', 'replicated', 'replicated');


create table dict.dict_scd_type
(
    scd_type_rk   smallint   not null
        constraint scd_type_pk
            primary key,
    scd_type_cd   dict.dname not null,
    scd_type_name dict.dname not null,
    scd_type_desc text
);
insert into dict.dict_scd_type (scd_type_rk, scd_type_cd, scd_type_name, scd_type_desc) values (-1, 'undef', 'undefined', 'Не определено');
insert into dict.dict_scd_type (scd_type_rk, scd_type_cd, scd_type_name, scd_type_desc) values (0, 'scd_0', 'scd_0', 'immutable');
insert into dict.dict_scd_type (scd_type_rk, scd_type_cd, scd_type_name, scd_type_desc) values (1, 'scd_1', 'scd_1', 'mutable');
insert into dict.dict_scd_type (scd_type_rk, scd_type_cd, scd_type_name, scd_type_desc) values (2, 'scd_2', 'scd_2', 'effective');
insert into dict.dict_scd_type (scd_type_rk, scd_type_cd, scd_type_name, scd_type_desc) values (3, 'scd_2_range', 'scd_2_range', 'effective_from/to');


create table dict.dict_data_type
(
    data_type_rk     smallint   not null
      constraint dict_data_type_pk
         primary key,
    data_type_cd     dict.dname not null,
    data_type_name   dict.dname not null,
    data_type_desc   text,
    is_boolean_flg   boolean    not null,
    is_integer_flg   boolean    not null,
    is_decimal_flg   boolean    not null,
    is_real_flg      boolean    not null,
    is_date_flg      boolean    not null,
    is_time_flg      boolean    not null,
    is_timestamp_flg boolean    not null,
    is_interval_flg  boolean    not null,
    is_character_flg boolean    not null,
    is_uuid_flg      boolean    not null,
    is_json_flg      boolean    not null
);
insert into dict.dict_data_type (data_type_rk, data_type_cd, data_type_name, data_type_desc, is_boolean_flg, is_integer_flg, is_decimal_flg, is_real_flg, is_date_flg, is_time_flg, is_timestamp_flg, is_interval_flg, is_character_flg, is_uuid_flg, is_json_flg) values (1, 'boolean', 'boolean', 'Логический тип данных', true, false, false, false, false, false, false, false, false, false, false);
insert into dict.dict_data_type (data_type_rk, data_type_cd, data_type_name, data_type_desc, is_boolean_flg, is_integer_flg, is_decimal_flg, is_real_flg, is_date_flg, is_time_flg, is_timestamp_flg, is_interval_flg, is_character_flg, is_uuid_flg, is_json_flg) values (2, 'smallint', 'smallint', 'Целое число (-32768 .. +32767)', false, true, false, false, false, false, false, false, false, false, false);
insert into dict.dict_data_type (data_type_rk, data_type_cd, data_type_name, data_type_desc, is_boolean_flg, is_integer_flg, is_decimal_flg, is_real_flg, is_date_flg, is_time_flg, is_timestamp_flg, is_interval_flg, is_character_flg, is_uuid_flg, is_json_flg) values (3, 'integer', 'integer', 'Целое число (-2147483648 .. +2147483647)', false, true, false, false, false, false, false, false, false, false, false);
insert into dict.dict_data_type (data_type_rk, data_type_cd, data_type_name, data_type_desc, is_boolean_flg, is_integer_flg, is_decimal_flg, is_real_flg, is_date_flg, is_time_flg, is_timestamp_flg, is_interval_flg, is_character_flg, is_uuid_flg, is_json_flg) values (4, 'bigint', 'bigint', 'Целое число (-9223372036854775808 .. 9223372036854775807)', false, true, false, false, false, false, false, false, false, false, false);
insert into dict.dict_data_type (data_type_rk, data_type_cd, data_type_name, data_type_desc, is_boolean_flg, is_integer_flg, is_decimal_flg, is_real_flg, is_date_flg, is_time_flg, is_timestamp_flg, is_interval_flg, is_character_flg, is_uuid_flg, is_json_flg) values (5, 'numeric', 'numeric', 'Вещественное число с переменной точностью (до 131072 цифр до десятичной точки и до 16383 — после)', false, false, true, false, false, false, false, false, false, false, false);
insert into dict.dict_data_type (data_type_rk, data_type_cd, data_type_name, data_type_desc, is_boolean_flg, is_integer_flg, is_decimal_flg, is_real_flg, is_date_flg, is_time_flg, is_timestamp_flg, is_interval_flg, is_character_flg, is_uuid_flg, is_json_flg) values (6, 'real', 'real', 'Вещественное число с переменной точностью (точность в пределах 6 десятичных цифр)', false, false, false, true, false, false, false, false, false, false, false);
insert into dict.dict_data_type (data_type_rk, data_type_cd, data_type_name, data_type_desc, is_boolean_flg, is_integer_flg, is_decimal_flg, is_real_flg, is_date_flg, is_time_flg, is_timestamp_flg, is_interval_flg, is_character_flg, is_uuid_flg, is_json_flg) values (7, 'double', 'double precision', 'Вещественное число с переменной точностью (точность в пределах 15 десятичных цифр)', false, false, false, true, false, false, false, false, false, false, false);
insert into dict.dict_data_type (data_type_rk, data_type_cd, data_type_name, data_type_desc, is_boolean_flg, is_integer_flg, is_decimal_flg, is_real_flg, is_date_flg, is_time_flg, is_timestamp_flg, is_interval_flg, is_character_flg, is_uuid_flg, is_json_flg) values (8, 'date', 'date', 'Дата', false, false, false, false, true, false, false, false, false, false, false);
insert into dict.dict_data_type (data_type_rk, data_type_cd, data_type_name, data_type_desc, is_boolean_flg, is_integer_flg, is_decimal_flg, is_real_flg, is_date_flg, is_time_flg, is_timestamp_flg, is_interval_flg, is_character_flg, is_uuid_flg, is_json_flg) values (9, 'timestamp', 'timestamp without time zone', 'Дата и время без часового пояса', false, false, false, false, false, false, true, false, false, false, false);
insert into dict.dict_data_type (data_type_rk, data_type_cd, data_type_name, data_type_desc, is_boolean_flg, is_integer_flg, is_decimal_flg, is_real_flg, is_date_flg, is_time_flg, is_timestamp_flg, is_interval_flg, is_character_flg, is_uuid_flg, is_json_flg) values (10, 'timestamptz', 'timestamp with time zone', 'Дата и время с часовым поясом', false, false, false, false, false, false, true, false, false, false, false);
insert into dict.dict_data_type (data_type_rk, data_type_cd, data_type_name, data_type_desc, is_boolean_flg, is_integer_flg, is_decimal_flg, is_real_flg, is_date_flg, is_time_flg, is_timestamp_flg, is_interval_flg, is_character_flg, is_uuid_flg, is_json_flg) values (11, 'time', 'time without time zone', 'Время без часового пояса', false, false, false, false, false, true, false, false, false, false, false);
insert into dict.dict_data_type (data_type_rk, data_type_cd, data_type_name, data_type_desc, is_boolean_flg, is_integer_flg, is_decimal_flg, is_real_flg, is_date_flg, is_time_flg, is_timestamp_flg, is_interval_flg, is_character_flg, is_uuid_flg, is_json_flg) values (12, 'timetz', 'time with time zone', 'Время с часовым поясом', false, false, false, false, false, true, false, false, false, false, false);
insert into dict.dict_data_type (data_type_rk, data_type_cd, data_type_name, data_type_desc, is_boolean_flg, is_integer_flg, is_decimal_flg, is_real_flg, is_date_flg, is_time_flg, is_timestamp_flg, is_interval_flg, is_character_flg, is_uuid_flg, is_json_flg) values (13, 'interval', 'interval', 'Временной интервал', false, false, false, false, false, false, false, true, false, false, false);
insert into dict.dict_data_type (data_type_rk, data_type_cd, data_type_name, data_type_desc, is_boolean_flg, is_integer_flg, is_decimal_flg, is_real_flg, is_date_flg, is_time_flg, is_timestamp_flg, is_interval_flg, is_character_flg, is_uuid_flg, is_json_flg) values (14, 'character', 'character', 'Cтрока фиксированной длины, дополненная пробелами', false, false, false, false, false, false, false, false, true, false, false);
insert into dict.dict_data_type (data_type_rk, data_type_cd, data_type_name, data_type_desc, is_boolean_flg, is_integer_flg, is_decimal_flg, is_real_flg, is_date_flg, is_time_flg, is_timestamp_flg, is_interval_flg, is_character_flg, is_uuid_flg, is_json_flg) values (15, 'varchar', 'varchar', 'Строка ограниченной переменной длины', false, false, false, false, false, false, false, false, true, false, false);
insert into dict.dict_data_type (data_type_rk, data_type_cd, data_type_name, data_type_desc, is_boolean_flg, is_integer_flg, is_decimal_flg, is_real_flg, is_date_flg, is_time_flg, is_timestamp_flg, is_interval_flg, is_character_flg, is_uuid_flg, is_json_flg) values (16, 'text', 'text', 'Строка неограниченной переменной длины', false, false, false, false, false, false, false, false, true, false, false);
insert into dict.dict_data_type (data_type_rk, data_type_cd, data_type_name, data_type_desc, is_boolean_flg, is_integer_flg, is_decimal_flg, is_real_flg, is_date_flg, is_time_flg, is_timestamp_flg, is_interval_flg, is_character_flg, is_uuid_flg, is_json_flg) values (17, 'uuid', 'uuid', 'Универсальные уникальные идентификаторы', false, false, false, false, false, false, false, false, false, true, false);
insert into dict.dict_data_type (data_type_rk, data_type_cd, data_type_name, data_type_desc, is_boolean_flg, is_integer_flg, is_decimal_flg, is_real_flg, is_date_flg, is_time_flg, is_timestamp_flg, is_interval_flg, is_character_flg, is_uuid_flg, is_json_flg) values (18, 'json', 'json', 'Текстовый формат для хранения и передачи структурированных данных', false, false, false, false, false, false, false, false, false, false, true);
insert into dict.dict_data_type (data_type_rk, data_type_cd, data_type_name, data_type_desc, is_boolean_flg, is_integer_flg, is_decimal_flg, is_real_flg, is_date_flg, is_time_flg, is_timestamp_flg, is_interval_flg, is_character_flg, is_uuid_flg, is_json_flg) values (19, 'jsonb', 'jsonb', 'Бинанрный формат для хранения и передачи структурированных данных', false, false, false, false, false, false, false, false, false, false, true);


create table dict.dict_data_domain
(
    data_domain_rk       smallint   not null
        constraint dict_data_domain_pk
            primary key,
    data_domain_cd       dict.dname not null,
    data_domain_name     dict.dname not null,
    data_domain_desc     text,
    attribute_name_regex text,
    data_type_rk         smallint   not null,
    data_type_size_cnt   int,
    data_type_scale_cnt  int
);

INSERT INTO dict.dict_data_domain (data_domain_rk, data_domain_cd, data_domain_name, data_domain_desc, attribute_name_regex, data_type_rk, data_type_size_cnt, data_type_scale_cnt) VALUES (-1, 'undef', 'undefined', 'Не определено', null, -1, null, null);
INSERT INTO dict.dict_data_domain (data_domain_rk, data_domain_cd, data_domain_name, data_domain_desc, attribute_name_regex, data_type_rk, data_type_size_cnt, data_type_scale_cnt) VALUES (1, 'dinvalididentificator', 'dInvalidIdentificator', 'Техническое поле, определяющее валидность бизнес ключа. = 0 - для валидных BK, >0 - для невалидных', 'invalid_id', 4, null, null);
INSERT INTO dict.dict_data_domain (data_domain_rk, data_domain_cd, data_domain_name, data_domain_desc, attribute_name_regex, data_type_rk, data_type_size_cnt, data_type_scale_cnt) VALUES (2, 'dversionidentificator', 'dVersionIdentificator', 'Идентификатор версии (используется механизмом версионности). В журнале транзакций хранится также информация о времени создания версии (к вопросу о необходимости create_dttm на слоях)', 'version_id', 4, null, null);
INSERT INTO dict.dict_data_domain (data_domain_rk, data_domain_cd, data_domain_name, data_domain_desc, attribute_name_regex, data_type_rk, data_type_size_cnt, data_type_scale_cnt) VALUES (3, 'dtoversionidentificator', 'dToVersionIdentificator', 'Идентификатор актуальности записи в рамках версионности (используется в объектах scd2)', 'to_version_id', 4, null, null);
INSERT INTO dict.dict_data_domain (data_domain_rk, data_domain_cd, data_domain_name, data_domain_desc, attribute_name_regex, data_type_rk, data_type_size_cnt, data_type_scale_cnt) VALUES (4, 'ddeletedflag', 'dDeletedFlag', 'Признак удаленной записи (используется механизмом версионности)', 'deleted_flg', 1, null, null);
INSERT INTO dict.dict_data_domain (data_domain_rk, data_domain_cd, data_domain_name, data_domain_desc, attribute_name_regex, data_type_rk, data_type_size_cnt, data_type_scale_cnt) VALUES (5, 'deffectivedate', 'dEffectiveDate', 'Бизнес-дата историчности (используется механизмом простой историчности)', 'effective_date', 8, null, null);
INSERT INTO dict.dict_data_domain (data_domain_rk, data_domain_cd, data_domain_name, data_domain_desc, attribute_name_regex, data_type_rk, data_type_size_cnt, data_type_scale_cnt) VALUES (6, 'deffectivebegindate', 'dEffectiveBeginDate', 'Бизнес-дата начала интервала историчности (используется механизмом полной историчности)', 'effective_from_date', 8, null, null);
INSERT INTO dict.dict_data_domain (data_domain_rk, data_domain_cd, data_domain_name, data_domain_desc, attribute_name_regex, data_type_rk, data_type_size_cnt, data_type_scale_cnt) VALUES (7, 'deffectiveenddate', 'dEffectiveEndDate', 'Бизнес-дата окончания интервала историчности (используется механизмом полной историчности)', 'effective_to_date', 8, null, null);
INSERT INTO dict.dict_data_domain (data_domain_rk, data_domain_cd, data_domain_name, data_domain_desc, attribute_name_regex, data_type_rk, data_type_size_cnt, data_type_scale_cnt) VALUES (8, 'dhashidentificator', 'dHashIdentificator', 'Хэш (алгоритм MD5) для атрибутов таблицы', 'hash_diff', 14, 32, null);
INSERT INTO dict.dict_data_domain (data_domain_rk, data_domain_cd, data_domain_name, data_domain_desc, attribute_name_regex, data_type_rk, data_type_size_cnt, data_type_scale_cnt) VALUES (9, 'deffectivebegindttm', 'dEffectiveBeginDttm', 'Бизнес-дата и время начала интервала историчности (используется механизмом полной историчности)', 'effective_from_dttm', 9, null, null);
INSERT INTO dict.dict_data_domain (data_domain_rk, data_domain_cd, data_domain_name, data_domain_desc, attribute_name_regex, data_type_rk, data_type_size_cnt, data_type_scale_cnt) VALUES (10, 'deffectiveenddttm', 'dEffectiveEndDttm', 'Бизнес-дата и время окончания интервала историчности (используется механизмом полной историчности)', 'effective_to_dttm', 9, null, null);
INSERT INTO dict.dict_data_domain (data_domain_rk, data_domain_cd, data_domain_name, data_domain_desc, attribute_name_regex, data_type_rk, data_type_size_cnt, data_type_scale_cnt) VALUES (11, 'deffectivedttm', 'dEffectiveDttm', 'Бизнес-дата и время историчности (используется механизмом простой историчности)', 'effective_dttm', 9, null, null);
INSERT INTO dict.dict_data_domain (data_domain_rk, data_domain_cd, data_domain_name, data_domain_desc, attribute_name_regex, data_type_rk, data_type_size_cnt, data_type_scale_cnt) VALUES (12, 'dbktype', 'dBkType', 'Тип бизнес-ключа', 'bk_type', 16, null, null);
INSERT INTO dict.dict_data_domain (data_domain_rk, data_domain_cd, data_domain_name, data_domain_desc, attribute_name_regex, data_type_rk, data_type_size_cnt, data_type_scale_cnt) VALUES (13, 'dretainedkey', 'dRetainedKey', 'Внутренний суррогатный ключ объекта, идентифицируемого на источнике', '%\_rk', 4, null, null);
INSERT INTO dict.dict_data_domain (data_domain_rk, data_domain_cd, data_domain_name, data_domain_desc, attribute_name_regex, data_type_rk, data_type_size_cnt, data_type_scale_cnt) VALUES (14, 'dsmallretainedkey', 'dSmallRetainedKey', 'Внутренний суррогатный ключ объекта, идентифицируемого на источнике c меньшим типом данных', '%\_rk', 2, null, null);
INSERT INTO dict.dict_data_domain (data_domain_rk, data_domain_cd, data_domain_name, data_domain_desc, attribute_name_regex, data_type_rk, data_type_size_cnt, data_type_scale_cnt) VALUES (15, 'dsurrogatekey', 'dSurrogateKey', e'Внутренний суррогатный ключ, не идентифицируемого на источнике (например, события или факта) Примечание: технически применим только для RDV.', '%\_sk', 4, null, null);
INSERT INTO dict.dict_data_domain (data_domain_rk, data_domain_cd, data_domain_name, data_domain_desc, attribute_name_regex, data_type_rk, data_type_size_cnt, data_type_scale_cnt) VALUES (16, 'dcode', 'dCode', 'Код, т.е. идентификатор, определяемый по стандарту (в т.ч. внутрибанковскому стандарту)', '%\_code', 16, 40, null);
INSERT INTO dict.dict_data_domain (data_domain_rk, data_domain_cd, data_domain_name, data_domain_desc, attribute_name_regex, data_type_rk, data_type_size_cnt, data_type_scale_cnt) VALUES (17, 'dcodeidentificator', 'dCodeIdentificator', 'Внутренний идентификатор простого справочника', '%\_cd', 16, 40, null);
INSERT INTO dict.dict_data_domain (data_domain_rk, data_domain_cd, data_domain_name, data_domain_desc, attribute_name_regex, data_type_rk, data_type_size_cnt, data_type_scale_cnt) VALUES (18, 'dnumber', 'dNumber', 'Номер, т.е. идентификатор, используемый людьми (номер паспорта, номер автомобиля)', '%\_num', 16, 25, null);
INSERT INTO dict.dict_data_domain (data_domain_rk, data_domain_cd, data_domain_name, data_domain_desc, attribute_name_regex, data_type_rk, data_type_size_cnt, data_type_scale_cnt) VALUES (19, 'dnumberlarge', 'dNumberLarge', 'Номер, т.е. идентификатор, используемый людьми (номер паспорта, номер автомобиля) с большим типом данных.', '%\_num', 16, 256, null);
INSERT INTO dict.dict_data_domain (data_domain_rk, data_domain_cd, data_domain_name, data_domain_desc, attribute_name_regex, data_type_rk, data_type_size_cnt, data_type_scale_cnt) VALUES (20, 'dnumberorder', 'dNumberOrder', 'Порядковый номер', '%\_no', 2, null, null);
INSERT INTO dict.dict_data_domain (data_domain_rk, data_domain_cd, data_domain_name, data_domain_desc, attribute_name_regex, data_type_rk, data_type_size_cnt, data_type_scale_cnt) VALUES (21, 'didentificator', 'dIdentificator', 'Внешний идентификатор ИС (например, идентификатор АБС, номер транзакции)', '%\_id', 16, 50, null);
INSERT INTO dict.dict_data_domain (data_domain_rk, data_domain_cd, data_domain_name, data_domain_desc, attribute_name_regex, data_type_rk, data_type_size_cnt, data_type_scale_cnt) VALUES (22, 'dbusinesskey', 'dBusinessKey', 'Натуральный ключ источника, приведенный к каноническому виду', '%\_bk', 16, 32, null);
INSERT INTO dict.dict_data_domain (data_domain_rk, data_domain_cd, data_domain_name, data_domain_desc, attribute_name_regex, data_type_rk, data_type_size_cnt, data_type_scale_cnt) VALUES (23, 'dname', 'dName', e'Имя собственное или краткое наименование (подходит для UI, где таких имен много или из них делается выбор) Примечание 1: По умолчанию на русском языке, иначе на указанном языке например *[_eng]_name Примечание 2: Добавление суффиксов eng соответствует стандарту ISO3166-1 alpha-3.', '%\_name', 16, 256, null);
INSERT INTO dict.dict_data_domain (data_domain_rk, data_domain_cd, data_domain_name, data_domain_desc, attribute_name_regex, data_type_rk, data_type_size_cnt, data_type_scale_cnt) VALUES (24, 'ddescription', 'dDescription', e'Описание (подходит для UI, где такое описание единственное), комментарий, примечание без определенного формата. Примечание1: По умолчанию на русском языке, иначе на указанном языке, например *[_eng]_desc Примечание2: Добавление суффиксов eng соответствует стандарту ISO3166-1 alpha-3.', '%\_desc', 16, 2000, null);
INSERT INTO dict.dict_data_domain (data_domain_rk, data_domain_cd, data_domain_name, data_domain_desc, attribute_name_regex, data_type_rk, data_type_size_cnt, data_type_scale_cnt) VALUES (25, 'dtext', 'dText', 'Текст (например, документа) произвольной длины', '%\_txt', 16, null, null);
INSERT INTO dict.dict_data_domain (data_domain_rk, data_domain_cd, data_domain_name, data_domain_desc, attribute_name_regex, data_type_rk, data_type_size_cnt, data_type_scale_cnt) VALUES (26, 'dflag', 'dFlag', 'Признак', '%\_flg', 1, null, null);
INSERT INTO dict.dict_data_domain (data_domain_rk, data_domain_cd, data_domain_name, data_domain_desc, attribute_name_regex, data_type_rk, data_type_size_cnt, data_type_scale_cnt) VALUES (27, 'ddate', 'dDate', 'Бизнес-дата (нет часового пояса)', '%\_date', 8, null, null);
INSERT INTO dict.dict_data_domain (data_domain_rk, data_domain_cd, data_domain_name, data_domain_desc, attribute_name_regex, data_type_rk, data_type_size_cnt, data_type_scale_cnt) VALUES (28, 'ddatetime', 'dDateTime', 'Отметка времени (дата и время в часовом поясе).', '%\_dttm', 9, null, null);
INSERT INTO dict.dict_data_domain (data_domain_rk, data_domain_cd, data_domain_name, data_domain_desc, attribute_name_regex, data_type_rk, data_type_size_cnt, data_type_scale_cnt) VALUES (29, 'dsum', 'dSum', 'Сумма (в валюте)', '%\_sum', 5, null, null);
INSERT INTO dict.dict_data_domain (data_domain_rk, data_domain_cd, data_domain_name, data_domain_desc, attribute_name_regex, data_type_rk, data_type_size_cnt, data_type_scale_cnt) VALUES (30, 'dcount', 'dCount', 'Количество', '%\_cnt', 4, null, null);
INSERT INTO dict.dict_data_domain (data_domain_rk, data_domain_cd, data_domain_name, data_domain_desc, attribute_name_regex, data_type_rk, data_type_size_cnt, data_type_scale_cnt) VALUES (31, 'dpercent', 'dPercent', 'Процент', '%\_pc', 5, null, null);
INSERT INTO dict.dict_data_domain (data_domain_rk, data_domain_cd, data_domain_name, data_domain_desc, attribute_name_regex, data_type_rk, data_type_size_cnt, data_type_scale_cnt) VALUES (32, 'drate', 'dRate', 'Ставка (например, дохода, комиссии), скорость, частота', '%\_rate', 5, null, null);
INSERT INTO dict.dict_data_domain (data_domain_rk, data_domain_cd, data_domain_name, data_domain_desc, attribute_name_regex, data_type_rk, data_type_size_cnt, data_type_scale_cnt) VALUES (33, 'dscore', 'dScore', 'Скоринговый балл', '%\_score', 5, null, null);
INSERT INTO dict.dict_data_domain (data_domain_rk, data_domain_cd, data_domain_name, data_domain_desc, attribute_name_regex, data_type_rk, data_type_size_cnt, data_type_scale_cnt) VALUES (34, 'duuid', 'dUUID', e'Глобальный уникальный идентификатор сущности GUID, UUID Примечание: Длина определяется стандартом UUID/GUID в ISO/IEC 11578:1996.', '%\_uid', 17, null, null);
INSERT INTO dict.dict_data_domain (data_domain_rk, data_domain_cd, data_domain_name, data_domain_desc, attribute_name_regex, data_type_rk, data_type_size_cnt, data_type_scale_cnt) VALUES (35, 'durl', 'dURL', 'Ссылка', '%\_url', 16, null, null);
INSERT INTO dict.dict_data_domain (data_domain_rk, data_domain_cd, data_domain_name, data_domain_desc, attribute_name_regex, data_type_rk, data_type_size_cnt, data_type_scale_cnt) VALUES (36, 'dduration', 'dDuration', 'ВременнОе смещение в соответсвии со стандартами `duration iso 8601`', '%\_duration', 16, null, null);
INSERT INTO dict.dict_data_domain (data_domain_rk, data_domain_cd, data_domain_name, data_domain_desc, attribute_name_regex, data_type_rk, data_type_size_cnt, data_type_scale_cnt) VALUES (37, 'dinterval', 'dInterval', 'Хранение временнЫх интервалов', '%\_duration', 13, null, null);
INSERT INTO dict.dict_data_domain (data_domain_rk, data_domain_cd, data_domain_name, data_domain_desc, attribute_name_regex, data_type_rk, data_type_size_cnt, data_type_scale_cnt) VALUES (38, 'dintervalseconds', 'dIntervalSeconds', 'Задание интервалов в секундах', '%\_duration', 4, null, null);
INSERT INTO dict.dict_data_domain (data_domain_rk, data_domain_cd, data_domain_name, data_domain_desc, attribute_name_regex, data_type_rk, data_type_size_cnt, data_type_scale_cnt) VALUES (39, 'djson', 'dJSON', 'Данные в формате JSON', '%\_json', 18, null, null);
INSERT INTO dict.dict_data_domain (data_domain_rk, data_domain_cd, data_domain_name, data_domain_desc, attribute_name_regex, data_type_rk, data_type_size_cnt, data_type_scale_cnt) VALUES (40, 'dregexp', 'dRegexp', 'Данные в формаре регулярных сообщений', '%\_regexp', 16, null, null);
INSERT INTO dict.dict_data_domain (data_domain_rk, data_domain_cd, data_domain_name, data_domain_desc, attribute_name_regex, data_type_rk, data_type_size_cnt, data_type_scale_cnt) VALUES (41, 'dversion', 'dVersion', 'Номер версии в соответсии со стандартами https://semver.org', '%\_version', 16, null, null);
INSERT INTO dict.dict_data_domain (data_domain_rk, data_domain_cd, data_domain_name, data_domain_desc, attribute_name_regex, data_type_rk, data_type_size_cnt, data_type_scale_cnt) VALUES (42, 'dtechnicalbigint', 'dTechnicalBigint', 'Технический домен для данных типа BIGINT. Примечание: Используется только для атрибутов, не представленных на слое BDM.', '%\_tbi', 4, null, null);

create table dict.dict_metadata_type
(
    metadata_type_rk   smallint   not null
          constraint metadata_type_pk
             primary key,
    metadata_type_cd   dict.dname not null,
    metadata_type_name dict.dname not null,
    metadata_type_desc text
);
insert into dict.dict_metadata_type(metadata_type_rk, metadata_type_cd, metadata_type_name, metadata_type_desc) values (-1, 'undef', 'undefined', 'Не определено');
insert into dict.dict_metadata_type(metadata_type_rk, metadata_type_cd, metadata_type_name, metadata_type_desc) values (0, 'hash', 'hash', 'hash_diff');
insert into dict.dict_metadata_type(metadata_type_rk, metadata_type_cd, metadata_type_name, metadata_type_desc) values (1, 'version', 'version', 'version_id');
insert into dict.dict_metadata_type(metadata_type_rk, metadata_type_cd, metadata_type_name, metadata_type_desc) values (2, 'version_to', 'version_to', 'to_version_id');
insert into dict.dict_metadata_type(metadata_type_rk, metadata_type_cd, metadata_type_name, metadata_type_desc) values (3, 'effective', 'effective', 'effective_date, effective_from_date');
insert into dict.dict_metadata_type(metadata_type_rk, metadata_type_cd, metadata_type_name, metadata_type_desc) values (4, 'effective_to', 'effective_to', 'effective_to_date');
insert into dict.dict_metadata_type(metadata_type_rk, metadata_type_cd, metadata_type_name, metadata_type_desc) values (5, 'deleted', 'deleted', 'deleted_flg');
insert into dict.dict_metadata_type(metadata_type_rk, metadata_type_cd, metadata_type_name, metadata_type_desc) values (6, 'valid', 'valid', 'valid_flg');
insert into dict.dict_metadata_type(metadata_type_rk, metadata_type_cd, metadata_type_name, metadata_type_desc) values (7, 'invalid', 'invalid', 'invalid_id');
insert into dict.dict_metadata_type(metadata_type_rk, metadata_type_cd, metadata_type_name, metadata_type_desc) values (8, 'source', 'source', 'src_cd');
insert into dict.dict_metadata_type(metadata_type_rk, metadata_type_cd, metadata_type_name, metadata_type_desc) values (9, 'key_source', 'key_source', 'bk_src_cd');
insert into dict.dict_metadata_type(metadata_type_rk, metadata_type_cd, metadata_type_name, metadata_type_desc) values (10, 'source_key', 'source_key', 'bk_id');
insert into dict.dict_metadata_type(metadata_type_rk, metadata_type_cd, metadata_type_name, metadata_type_desc) values (11, 'source_key_type', 'source_key_type', 'bk_type');
insert into dict.dict_metadata_type(metadata_type_rk, metadata_type_cd, metadata_type_name, metadata_type_desc) values (12, 'record_type', 'record_type', 'synth_code');

create table dict.dict_domain
(
    domain_rk   smallint   not null
        constraint dict_domain_pk
           primary key,
    domain_cd   dict.dname not null,
    domain_name dict.dname not null,
    domain_desc text
);
insert into dict.dict_domain (domain_rk, domain_cd, domain_name, domain_desc) values (1, 'вовлеченная сторона', 'вовлеченная сторона', 'Вовлеченная сторона');
insert into dict.dict_domain (domain_rk, domain_cd, domain_name, domain_desc) values (2, 'главная книга', 'главная книга', 'Главная книга');
insert into dict.dict_domain (domain_rk, domain_cd, domain_name, domain_desc) values (3, 'вхд', 'вхд', 'ВнутриХозяйственная Деятельность');
insert into dict.dict_domain (domain_rk, domain_cd, domain_name, domain_desc) values (4, 'фи общие', 'фи общие', 'Финансовый Инструмент общие');
insert into dict.dict_domain (domain_rk, domain_cd, domain_name, domain_desc) values (5, 'фи валюта металл', 'фи валюта металл', 'Финансовый Инструмент валюта металл');
insert into dict.dict_domain (domain_rk, domain_cd, domain_name, domain_desc) values (6, 'фи ценная бумага', 'фи ценная бумага', 'Финансовый Инструмент ценная бумага');
insert into dict.dict_domain (domain_rk, domain_cd, domain_name, domain_desc) values (7, 'фи пфи', 'фи пфи', 'Финансовый Инструмент ПФИ');
insert into dict.dict_domain (domain_rk, domain_cd, domain_name, domain_desc) values (8, 'сделки общие', 'сделки общие', 'Сделки общие');
insert into dict.dict_domain (domain_rk, domain_cd, domain_name, domain_desc) values (9, 'сделки кредит', 'сделки кредит', 'Сделки кредит');
insert into dict.dict_domain (domain_rk, domain_cd, domain_name, domain_desc) values (10, 'сделки портфель', 'сделки портфель', 'Сделки портфель');
insert into dict.dict_domain (domain_rk, domain_cd, domain_name, domain_desc) values (11, 'сделки обеспечение', 'сделки обеспечение', 'Сделки обеспечение');
insert into dict.dict_domain (domain_rk, domain_cd, domain_name, domain_desc) values (12, 'сделки гос программы', 'сделки гос программы', 'Сделки государственные программы');
insert into dict.dict_domain (domain_rk, domain_cd, domain_name, domain_desc) values (13, 'сделки фондирование', 'сделки фондирование', 'Сделки фондирование');
insert into dict.dict_domain (domain_rk, domain_cd, domain_name, domain_desc) values (14, 'сделки продукт', 'сделки продукт', 'Сделки продукт');
insert into dict.dict_domain (domain_rk, domain_cd, domain_name, domain_desc) values (15, 'сделки страхование', 'сделки страхование', 'Сделки страхование');
insert into dict.dict_domain (domain_rk, domain_cd, domain_name, domain_desc) values (16, 'сделки депозит', 'сделки депозит', 'Сделки депозит');
insert into dict.dict_domain (domain_rk, domain_cd, domain_name, domain_desc) values (17, 'сделки банковская карта', 'сделки банковская карта', 'Сделки банковская карта');
insert into dict.dict_domain (domain_rk, domain_cd, domain_name, domain_desc) values (18, 'сделки рко', 'сделки рко', 'Сделки Расчетно-Кассовое Обслуживание');
insert into dict.dict_domain (domain_rk, domain_cd, domain_name, domain_desc) values (19, 'расчетный центр', 'расчетный центр', 'Расчетный центр');
insert into dict.dict_domain (domain_rk, domain_cd, domain_name, domain_desc) values (20, 'заявки', 'заявки', 'Заявки');
insert into dict.dict_domain (domain_rk, domain_cd, domain_name, domain_desc) values (21, 'сделки документарный бизнес', 'сделки документарный бизнес', 'Сделки документарный бизнес');
insert into dict.dict_domain (domain_rk, domain_cd, domain_name, domain_desc) values (22, 'сделки денежный рынок', 'сделки денежный рынок', 'Сделки денежный рынок');
insert into dict.dict_domain (domain_rk, domain_cd, domain_name, domain_desc) values (23, 'сделки рцб', 'сделки рцб', 'Сделки Рынок Ценных Бумаг');
insert into dict.dict_domain (domain_rk, domain_cd, domain_name, domain_desc) values (24, 'сделки мбк мбд', 'сделки мбк мбд', 'Сделки межбанковский рынок (МБК - кредиты, МБД - депозиты)');
insert into dict.dict_domain (domain_rk, domain_cd, domain_name, domain_desc) values (25, 'сделки дбо', 'сделки дбо', 'Сделки Дистанционное Банковское Обслуживание');
insert into dict.dict_domain (domain_rk, domain_cd, domain_name, domain_desc) values (26, 'сделки банковские сейфы', 'сделки банковские сейфы', 'Сделки банковские сейфы');
insert into dict.dict_domain (domain_rk, domain_cd, domain_name, domain_desc) values (27, 'сделки эквайринг', 'сделки эквайринг', 'Сделки эквайринг');
insert into dict.dict_domain (domain_rk, domain_cd, domain_name, domain_desc) values (28, 'сделки эскроу', 'сделки эскроу', 'Сделки эскроу');
insert into dict.dict_domain (domain_rk, domain_cd, domain_name, domain_desc) values (29, 'сделки зарплатный проект', 'сделки зарплатный проект', 'Сделки зарплатный проект');
insert into dict.dict_domain (domain_rk, domain_cd, domain_name, domain_desc) values (30, 'сделки секьюритизация', 'сделки секьюритизация', 'Сделки секьюритизация');
insert into dict.dict_domain (domain_rk, domain_cd, domain_name, domain_desc) values (31, 'сделки вложения в пиф', 'сделки вложения в пиф', 'Сделки вложения в пиф');
insert into dict.dict_domain (domain_rk, domain_cd, domain_name, domain_desc) values (32, 'коллекторская активность', 'коллекторская активность', 'Коллекторская активность');
insert into dict.dict_domain (domain_rk, domain_cd, domain_name, domain_desc) values (33, 'риски', 'риски', 'Риски');
insert into dict.dict_domain (domain_rk, domain_cd, domain_name, domain_desc) values (34, 'клиентские стратегии', 'клиентские стратегии', 'Клиентские стратегии');
insert into dict.dict_domain (domain_rk, domain_cd, domain_name, domain_desc) values (35, 'клиентские сервисы', 'клиентские сервисы', 'Клиентские сервисы');
insert into dict.dict_domain (domain_rk, domain_cd, domain_name, domain_desc) values (36, 'внутренний учет', 'внутренний учет', 'Внутренний учет');
insert into dict.dict_domain (domain_rk, domain_cd, domain_name, domain_desc) values (37, 'общие', 'общие', 'Общие');
insert into dict.dict_domain (domain_rk, domain_cd, domain_name, domain_desc) values (38, 'сделки брокерское обслуживание', 'сделки брокерское обслуживание', 'Сделки брокерское обслуживание');
insert into dict.dict_domain (domain_rk, domain_cd, domain_name, domain_desc) values (39, 'сделки драгоценные металлы', 'сделки драгоценные металлы', 'Сделки драгоценные металлы');
insert into dict.dict_domain (domain_rk, domain_cd, domain_name, domain_desc) values (40, 'депозитарий', 'депозитарий', 'Депозитарий');
insert into dict.dict_domain (domain_rk, domain_cd, domain_name, domain_desc) values (41, 'сделки цессия', 'сделки цессия', 'Сделки цессия');
insert into dict.dict_domain (domain_rk, domain_cd, domain_name, domain_desc) values (42, 'валютный контроль', 'валютный контроль', 'Валютный контроль');
insert into dict.dict_domain (domain_rk, domain_cd, domain_name, domain_desc) values (43, 'кассовые операции', 'кассовые операции', 'Кассовые операции');


create table dict.dict_service_type (
	service_type_rk   smallint   not null
         constraint dict_service_type_pk
            primary key,
	service_type_cd   dict.dname not null,
	service_type_name dict.dname,
	service_type_desc text,
	is_etl_flg        boolean    not null,
	is_rdbms_flg      boolean    not null,
	is_core_flg       boolean    not null
);
insert into dict.dict_service_type (service_type_rk, service_type_cd, service_type_name, service_type_desc, is_etl_flg, is_rdbms_flg, is_core_flg) values (1, 'instance_AF', 'Instance AirFlow', 'Инстанс экземпляра AirFlow', true, false, false);
insert into dict.dict_service_type (service_type_rk, service_type_cd, service_type_name, service_type_desc, is_etl_flg, is_rdbms_flg, is_core_flg) values (2, 'adgp', 'ADGP', 'Сервис раскатки ADGP', false, false, true);


create table dict.dict_environment (
	environment_rk      smallint   not null
        constraint dict_environment_pk
           primary key,
	environment_cd      dict.dname not null,
	environment_name    dict.dname,
	environment_desc    text,
	is_dev_environment  boolean    not null,
	is_ift_environment  boolean    not null,
	is_test_environment boolean    not null,
	is_prod_environment boolean    not null
);
insert into dict.dict_environment (environment_rk, environment_cd, environment_name, environment_desc, is_dev_environment, is_ift_environment, is_test_environment, is_prod_environment) values (1, 'dev', 'dev', 'airflow service', true, false, false, false);
insert into dict.dict_environment (environment_rk, environment_cd, environment_name, environment_desc, is_dev_environment, is_ift_environment, is_test_environment, is_prod_environment) values (2, 'if', 'ift', 'airflow service', false, true, false, false);
insert into dict.dict_environment (environment_rk, environment_cd, environment_name, environment_desc, is_dev_environment, is_ift_environment, is_test_environment, is_prod_environment) values (3, 'rr', 'preprod', 'airflow service', false, false, true, false);
insert into dict.dict_environment (environment_rk, environment_cd, environment_name, environment_desc, is_dev_environment, is_ift_environment, is_test_environment, is_prod_environment) values (4, 'p0', 'prod', 'airflow service', false, false, false, true);


create table dict.link_alias_data_type
(
    data_type_source_cd dict.dname not null,
    alias_data_type_cd  dict.dname not null,
    alias_data_type_id  smallint,
    data_type_rk        smallint   not null,
    constraint link_alias_data_type_pk
        primary key (data_type_source_cd, alias_data_type_cd)
);
insert into dict.link_alias_data_type (data_type_source_cd, alias_data_type_cd, alias_data_type_id, data_type_rk) values ('etl_dm', 'bool', null, 1);
insert into dict.link_alias_data_type (data_type_source_cd, alias_data_type_cd, alias_data_type_id, data_type_rk) values ('etl_dm', 'int', null, 3);
insert into dict.link_alias_data_type (data_type_source_cd, alias_data_type_cd, alias_data_type_id, data_type_rk) values ('etl_dm', 'float', null, 7);
insert into dict.link_alias_data_type (data_type_source_cd, alias_data_type_cd, alias_data_type_id, data_type_rk) values ('etl_dm', 'str', null, 16);
insert into dict.link_alias_data_type (data_type_source_cd, alias_data_type_cd, alias_data_type_id, data_type_rk) values ('etl_dm', 'list', null, 19);
insert into dict.link_alias_data_type (data_type_source_cd, alias_data_type_cd, alias_data_type_id, data_type_rk) values ('etl_dm', 'dict', null, 19);
insert into dict.link_alias_data_type (data_type_source_cd, alias_data_type_cd, alias_data_type_id, data_type_rk) values ('physical_dm', 'boolean', 16, 1);
insert into dict.link_alias_data_type (data_type_source_cd, alias_data_type_cd, alias_data_type_id, data_type_rk) values ('physical_dm', 'smallint', 21, 2);
insert into dict.link_alias_data_type (data_type_source_cd, alias_data_type_cd, alias_data_type_id, data_type_rk) values ('physical_dm', 'integer', 23, 3);
insert into dict.link_alias_data_type (data_type_source_cd, alias_data_type_cd, alias_data_type_id, data_type_rk) values ('physical_dm', 'bigint', 20, 4);
insert into dict.link_alias_data_type (data_type_source_cd, alias_data_type_cd, alias_data_type_id, data_type_rk) values ('physical_dm', 'numeric', 1700, 5);
insert into dict.link_alias_data_type (data_type_source_cd, alias_data_type_cd, alias_data_type_id, data_type_rk) values ('physical_dm', 'real', 700, 6);
insert into dict.link_alias_data_type (data_type_source_cd, alias_data_type_cd, alias_data_type_id, data_type_rk) values ('physical_dm', 'double', 701, 7);
insert into dict.link_alias_data_type (data_type_source_cd, alias_data_type_cd, alias_data_type_id, data_type_rk) values ('physical_dm', 'character', 1042, 14);
insert into dict.link_alias_data_type (data_type_source_cd, alias_data_type_cd, alias_data_type_id, data_type_rk) values ('physical_dm', 'varchar', 1043, 15);
insert into dict.link_alias_data_type (data_type_source_cd, alias_data_type_cd, alias_data_type_id, data_type_rk) values ('physical_dm', 'text', 25, 16);
insert into dict.link_alias_data_type (data_type_source_cd, alias_data_type_cd, alias_data_type_id, data_type_rk) values ('physical_dm', 'date', 1082, 8);
insert into dict.link_alias_data_type (data_type_source_cd, alias_data_type_cd, alias_data_type_id, data_type_rk) values ('physical_dm', 'timestamp', 1114, 9);
insert into dict.link_alias_data_type (data_type_source_cd, alias_data_type_cd, alias_data_type_id, data_type_rk) values ('physical_dm', 'timestamptz', 1184, 10);
insert into dict.link_alias_data_type (data_type_source_cd, alias_data_type_cd, alias_data_type_id, data_type_rk) values ('physical_dm', 'time', 1083, 11);
insert into dict.link_alias_data_type (data_type_source_cd, alias_data_type_cd, alias_data_type_id, data_type_rk) values ('physical_dm', 'timetz', 1266, 12);
insert into dict.link_alias_data_type (data_type_source_cd, alias_data_type_cd, alias_data_type_id, data_type_rk) values ('physical_dm', 'interval', 1186, 13);
insert into dict.link_alias_data_type (data_type_source_cd, alias_data_type_cd, alias_data_type_id, data_type_rk) values ('physical_dm', 'uuid', 2950, 17);
insert into dict.link_alias_data_type (data_type_source_cd, alias_data_type_cd, alias_data_type_id, data_type_rk) values ('physical_dm', 'json', 114, 18);
insert into dict.link_alias_data_type (data_type_source_cd, alias_data_type_cd, alias_data_type_id, data_type_rk) values ('physical_dm', 'jsonb', 3802, 19);
insert into dict.link_alias_data_type (data_type_source_cd, alias_data_type_cd, alias_data_type_id, data_type_rk) values ('logical_dm', 'bigint', null, 4);
insert into dict.link_alias_data_type (data_type_source_cd, alias_data_type_cd, alias_data_type_id, data_type_rk) values ('logical_dm', 'boolean', null, 1);
insert into dict.link_alias_data_type (data_type_source_cd, alias_data_type_cd, alias_data_type_id, data_type_rk) values ('logical_dm', 'char', null, 14);
insert into dict.link_alias_data_type (data_type_source_cd, alias_data_type_cd, alias_data_type_id, data_type_rk) values ('logical_dm', 'date', null, 8);
insert into dict.link_alias_data_type (data_type_source_cd, alias_data_type_cd, alias_data_type_id, data_type_rk) values ('logical_dm', 'numeric', null, 5);
insert into dict.link_alias_data_type (data_type_source_cd, alias_data_type_cd, alias_data_type_id, data_type_rk) values ('logical_dm', 'smallint', null, 2);
insert into dict.link_alias_data_type (data_type_source_cd, alias_data_type_cd, alias_data_type_id, data_type_rk) values ('logical_dm', 'text', null, 16);
insert into dict.link_alias_data_type (data_type_source_cd, alias_data_type_cd, alias_data_type_id, data_type_rk) values ('logical_dm', 'timestamp', null, 9);
insert into dict.link_alias_data_type (data_type_source_cd, alias_data_type_cd, alias_data_type_id, data_type_rk) values ('logical_dm', 'uuid', null, 17);


create table dict.dict_flow_resource_link_type (
    flow_resource_link_type_rk      smallint    not null,
	flow_resource_link_type_cd      dict.dname  not null,
	flow_resource_link_type_name    dict.dname,
	flow_resource_link_type_desc    text,

    constraint flow_resource_link_type_pk
        primary key (flow_resource_link_type_rk)
);

insert into dict.dict_flow_resource_link_type (flow_resource_link_type_rk, flow_resource_link_type_cd, flow_resource_link_type_name, flow_resource_link_type_desc)
     values (-1, 'undefined', 'undefined', 'undefined');
insert into dict.dict_flow_resource_link_type (flow_resource_link_type_rk, flow_resource_link_type_cd, flow_resource_link_type_name, flow_resource_link_type_desc)
     values (1, 'target', 'target', 'target');
insert into dict.dict_flow_resource_link_type (flow_resource_link_type_rk, flow_resource_link_type_cd, flow_resource_link_type_name, flow_resource_link_type_desc)
     values (2, 'source', 'source', 'source');


create sequence dict.dict_code_delivery_seq
	increment by 1
	minvalue 0
	maxvalue 9223372036854775807
	start with 219
	cache 100
	no cycle
	owned by dict.dict_code_delivery.code_delivery_rk;

alter table dict.dict_code_delivery
  alter column code_delivery_rk set default nextval('dict.dict_code_delivery_seq'::regclass);