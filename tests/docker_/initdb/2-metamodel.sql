create schema metamodel;

create sequence metamodel.md_seq
    start with 1
    minvalue 0
    cache 100;

create table metamodel.bridge_version (
    version_rk              bigint          not null  default nextval('metamodel.md_seq'::regclass),
    code_delivery_rk        smallint        not null,
    release_major_num       smallint        not null,
    release_minor_num       smallint        not null,
    release_fix_num         smallint        not null,
    release_desc            text,
	release_build_dttm      timestamptz,
	release_delivery_dttm   timestamptz     not null,

    constraint bridge_version_pk
        primary key (version_rk)
);

create unique index bridge_version_uk
    on metamodel.bridge_version (release_major_num,
                                 release_minor_num,
                                 release_fix_num,
                                 code_delivery_rk);


create table metamodel.bridge_vcs_repository_version (
	vcs_repository_version_rk   bigint      not null    default nextval('metamodel.md_seq'::regclass),
	vcs_repository_rk           smallint    not null,
	vcs_revision_id             text,
    code_delivery_rk            smallint    not null,
	version_rk                  bigint      not null,
	effective_from_dttm         timestamptz not null,
	effective_to_dttm           timestamptz not null,
	deleted_flg                 boolean     not null,

    constraint bridge_vcs_repository_version_pk
        primary key (vcs_repository_version_rk, effective_from_dttm)
);


create table metamodel.bridge_flow (
    flow_rk                 bigint      not null    default nextval('metamodel.md_seq'::regclass) ,
    flow_type_rk            smallint,
    source_data_layer_rk    smallint,
    target_data_layer_rk    smallint,
    flow_release_status_rk  smallint    not null,
    flow_name               dict.dname  not null,
    flow_desc               text,
	tag_list                text[],
    dag_json                jsonb,
	dag_hash                bigint,
	dag_task_json           jsonb,
	dag_task_hash           bigint,
    code_delivery_rk        smallint    not null,
    version_rk              bigint      not null,
    effective_from_dttm     timestamptz not null,
    effective_to_dttm       timestamptz not null,
    deleted_flg             boolean     not null,

    constraint bridge_flow_pk
        primary key (flow_rk, effective_from_dttm)
);

create index bridge_flow_name_idx
    on metamodel.bridge_flow (flow_name,
                              code_delivery_rk);
create index bridge_flow_tag_list_idx
    on metamodel.bridge_flow using gin (tag_list);


create table metamodel.link_flow (
    owner_flow_rk                   bigint         not null,
    source_flow_rk                  bigint         not null,
    target_flow_rk                  bigint         not null,
    flow_link_type_rk               smallint       not null,
    trigger_timeout_duration        bigint,
    retry_timeout_duration          bigint,
    delta_timeout_duration          bigint,
    flow_timeout_duration           bigint,
    trigger_cnt                     smallint,
    retry_cnt                       smallint,
    is_active_flg                   boolean,
    is_required_flg                 boolean,
    is_delta_required_flg           boolean,
    is_status_check_flg             boolean,
    is_resource_status_check_flg    boolean,
    code_delivery_rk                smallint       not null,
    version_rk                      bigint         not null,
    effective_from_dttm             timestamptz    not null,
    effective_to_dttm               timestamptz    not null,
    deleted_flg                     boolean        not null,

    constraint link_flow_pk
        primary key (target_flow_rk,
                     source_flow_rk,
                     owner_flow_rk,
                     flow_link_type_rk,
                     effective_from_dttm)
);

create index link_flow_source_idx
    on metamodel.link_flow (source_flow_rk);
create index link_flow_target_idx
    on metamodel.link_flow (target_flow_rk);


create table metamodel.bridge_flow_parameter (
    flow_parameter_rk   bigint      not null    default nextval('metamodel.md_seq'::regclass),
    flow_parameter_cd   dict.dname  not null,
    flow_parameter_desc text,
    flow_parameter_name dict.dname,
    data_type_rk        smallint    not null,
    default_value_json  jsonb,
    is_global_flg       boolean     not null,

    constraint bridge_flow_parameter_pk
        primary key (flow_parameter_rk)
);

create unique index bridge_flow_parameter_uk
    on metamodel.bridge_flow_parameter (flow_parameter_cd,
                                        data_type_rk);


create table metamodel.link_flow_parameter (
    flow_rk             bigint                   not null,
    flow_parameter_rk   bigint                   not null,
    default_value_json  jsonb,
    is_required_flg     boolean                  not null,
    is_in_flg           boolean                  not null,
    is_out_flg          boolean,
    code_delivery_rk    smallint                 not null,
    version_rk          bigint                   not null,
    effective_from_dttm timestamp with time zone not null,
    effective_to_dttm   timestamp with time zone not null,
    deleted_flg         boolean                  not null,

    constraint link_flow_parameter_pk
        primary key (flow_rk,
                     flow_parameter_rk,
                     effective_from_dttm)
);


create table metamodel.link_flow_parameter_pass (
    control_flow_rk      bigint     not null,
    work_flow_rk         bigint     not null,
    flow_parameter_rk    bigint     not null,
    flow_parameter_json  jsonb,
    is_value_flg         bool       not null,
    code_delivery_rk     smallint   not null,

    effective_from_dttm timestamptz not null,
    effective_to_dttm   timestamptz not null,
    deleted_flg         boolean     not null,
    version_rk          bigint      not null,

    constraint link_flow_parameter_pass_pk primary key (
        control_flow_rk,
        work_flow_rk,
        flow_parameter_rk,
        effective_from_dttm
    )
);


create table metamodel.link_source_delivery (
	source_rk           bigint      not null,
	code_delivery_rk    smallint    not null,
    effective_from_dttm timestamptz not null,
    effective_to_dttm   timestamptz not null,
    deleted_flg         boolean     not null,
	constraint link_source_delivery_pk primary key (source_rk, code_delivery_rk, effective_from_dttm)
);

create table metamodel.bridge_resource (
    resource_rk bigint not null default nextval('metamodel.md_seq'::regclass),
    resource_cd dict.dname not null,
    resource_desc text,
    is_readonly_flg boolean not null,
    is_maintenance_flg boolean not null,
    tag_list text[],
    resource_json jsonb not null,
    version_rk bigint not null,
    effective_from_dttm timestamptz not null,
    effective_to_dttm timestamptz not null,
    deleted_flg boolean not null,
    constraint bridge_resource_pk primary key (resource_rk, effective_from_dttm)
);
create index bridge_resource_uk
    on metamodel.bridge_resource (resource_cd, effective_from_dttm);


create table metamodel.bridge_source (
    source_rk           bigint      default nextval('metamodel.md_seq'::regclass) not null,
    parent_source_rk    bigint,
    source_cd           dict.dname  not null,
    ris_src_id          dict.dname  not null,
    ris_src_code        dict.dname  not null,
    short_cd            dict.dname,
    source_desc         text,
    data_layer_rk       smallint,
    effective_from_dttm timestamptz not null,
    effective_to_dttm   timestamptz not null,
    deleted_flg         boolean     not null
);
alter table metamodel.bridge_source
    add constraint bridge_source_pk
        primary key (source_rk, effective_from_dttm);
create index bridge_source_ux
    on metamodel.bridge_source (source_cd, effective_from_dttm);
create unique index bridge_source_unique
    on metamodel.bridge_source (ris_src_id, effective_from_dttm);
;

create table metamodel.bridge_logical_domain (
    logical_domain_rk           bigint      default nextval('metamodel.md_seq'::regclass) not null,

    effective_from_dttm         timestamptz not null,
    effective_to_dttm           timestamptz not null,
    deleted_flg                 boolean     not null,
    version_rk                  bigint      not null,

    domain_rk	                smallint    not null,
    domain_name	                text        not null,   -- subject_area
    data_layer_rk               smallint    not null,
    model_major_version_num	    smallint    not null,   -- model_version
    model_minor_version_num	    smallint    not null,   -- model_version
    model_effective_date	    date        not null    -- model_af_of_date
);
alter table metamodel.bridge_logical_domain
    add constraint bridge_logical_domain_pk
        primary key (logical_domain_rk, effective_from_dttm);
create unique index bridge_logical_domain_nk
    on metamodel.bridge_logical_domain (domain_name, data_layer_rk, effective_from_dttm);


create table metamodel.bridge_logical_table (
    logical_table_rk                bigint      default nextval('metamodel.md_seq'::regclass) not null,

    effective_from_dttm             timestamptz not null,
    effective_to_dttm               timestamptz not null,
    deleted_flg                     boolean     not null,
    version_rk                      bigint      not null,

    source_rk                       bigint      not null,
    source_cd                       text        not null,
    data_layer_rk                   smallint    not null,
    schema_name	                    text        not null,       -- table_owner
    table_name	                    text        not null,       -- table_code
    table_extra_name	            text,                       -- table_name
    table_desc	                    text,                       -- table_comment
    table_extra_desc	            text,                       -- table_annotation
    is_dict_flg                     bool        not null,       -- table_stereotype
    is_map_flg                      bool        not null,       -- table_stereotype
    is_hub_flg                      bool        not null,       -- table_stereotype
    is_sal_flg                      bool        not null,       -- table_stereotype
    is_mart_flg                     bool        not null,       -- table_stereotype
    is_bridge_flg                   bool        not null,       -- table_stereotype
    is_link_flg                     bool        not null,       -- table_stereotype
    table_data_vault_type_cd	    text,                       -- table_stereotype
    version_scd_type_rk             smallint    not null,
    history_scd_type_rk             smallint    not null,
    attribute_cnt  	                smallint,                   -- columns_count
    is_ref_flg	                    bool        not null,       -- table_isShortcut
    logical_domain_rk	            bigint      not null,       -- subject_area
    domain_rk	                    smallint    not null,
    domain_name	                    text,                       -- subject_area
    sub_domain_name	                text,                       -- sub_subject_area
    extra_sub_domain_name           text,                       -- sub_subject_area_additional
    axon_id     	                text,                       -- Axon_ID_table
    axon_url    	                text,                       -- Axon_URL_table
    model_major_version_from_num    smallint    not null,       -- table_version
    model_minor_version_from_num    smallint    not null,       -- table_version
    model_major_version_to_num      smallint,                   -- table_deprecated_version
    model_minor_version_to_num      smallint,                   -- table_deprecated_version
    is_deprecated_flg               bool        not null,
    model_effective_date	        date        not null        -- model_af_of_date
);
alter table metamodel.bridge_logical_table
    add constraint bridge_logical_table_pk
        primary key (logical_table_rk, effective_from_dttm);
create unique index bridge_logical_table_nk
    on metamodel.bridge_logical_table (table_name, schema_name, effective_from_dttm);


create table metamodel.bridge_logical_attribute (
    logical_attribute_rk            bigint      default nextval('metamodel.md_seq'::regclass) not null,

    effective_from_dttm             timestamptz not null,
    effective_to_dttm               timestamptz not null,
    deleted_flg                     boolean     not null,
    version_rk                      bigint      not null,

    record_no                       bigint      not null,   -- index
    schema_name	                    text        not null,   -- table_owner
    logical_table_rk                bigint      not null,
    table_name	                    text        not null,   -- table_code
    attribute_name	                text        not null,   -- column_code
    attribute_extra_name	        text,                   -- column_name
    data_type_rk	                smallint,               -- data_type
    data_type_cd	                text,                   -- data_type
    data_type_size_cnt              smallint,               -- data_type
    data_type_scale_cnt             smallint,               -- data_type
    is_nullable_flg	                boolean     not null,   -- mandatory
    data_domain_rk	                smallint,               -- domain
    data_domain_cd	                text,                   -- domain
    is_metadata_flg	                boolean     not null,   -- technical_column
    is_primary_key_flg	            boolean     not null,   -- primary_key
    is_foreign_key_flg	            boolean     not null,   -- foreign_key
    foreign_schema_name             text,                   -- parent_table_owner
    foreign_table_name              text,                   -- parent_table_code
    foreign_attribute_name          text,                   -- parent_column_code
    attribute_desc	                text,                   -- column_comment
    attribute_extra_desc	        text,                   -- column_annotation
    axon_id	                        text,                   -- Axon_ID
    axon_url	                    text,                   -- Axon_URL
    model_major_version_from_num    smallint    not null,   -- attribute_version
    model_minor_version_from_num    smallint    not null,   -- attribute_version
    model_major_version_to_num      smallint,               -- table_deprecated_version
    model_minor_version_to_num      smallint,               -- table_deprecated_version
    is_deprecated_flg               bool        not null,
    model_effective_date	        date        not null    -- model_af_of_date
);
alter table metamodel.bridge_logical_attribute
    add constraint bridge_logical_attribute_pk
        primary key (logical_attribute_rk, effective_from_dttm);
create unique index bridge_logical_attribute_tk
    on metamodel.bridge_logical_attribute (logical_table_rk, attribute_name, effective_from_dttm);
create unique index bridge_logical_attribute_nk
    on metamodel.bridge_logical_attribute (table_name, attribute_name, schema_name, effective_from_dttm);


create table metamodel.bridge_logical_key (
    logical_key_rk                  bigint      default nextval('metamodel.md_seq'::regclass) not null,

    effective_from_dttm             timestamptz not null,
    effective_to_dttm               timestamptz not null,
    deleted_flg                     boolean     not null,
    version_rk                      bigint      not null,

    key_name                        text        not null,
    schema_name                     text        not null,
    logical_table_rk                bigint      not null,
    table_name                      text        not null,
    is_primary_flg                  bool        not null,
    is_foreign_flg                  bool        not null,
    model_major_version_from_num    smallint    not null,
    model_minor_version_from_num    smallint    not null,
    model_major_version_to_num      smallint,
    model_minor_version_to_num      smallint,
    is_deprecated_flg               bool        not null,
    model_effective_date	        date        not null
);
alter table metamodel.bridge_logical_key
    add constraint bridge_logical_key_pk
        primary key (logical_key_rk, effective_from_dttm);
create unique index bridge_logical_key_nk
        on metamodel.bridge_logical_key (key_name, schema_name, effective_from_dttm);
create index bridge_logical_key_table_fk
        on metamodel.bridge_logical_key (logical_table_rk);


create table metamodel.link_logical_key_attribute (
    logical_key_rk                  bigint      not null,
    logical_attribute_rk            bigint      not null,

    effective_from_dttm             timestamptz not null,
    effective_to_dttm               timestamptz not null,
    deleted_flg                     boolean     not null,
    version_rk                      bigint      not null,

    schema_name                     text        not null,
    key_name                        text        not null,
    table_name                      text        not null,
    attribute_name                  text        not null,
    attribute_no                    smallint    not null,
    foreign_logical_key_rk          bigint,
    foreign_key_name                text,
    foreign_logical_attribute_rk    bigint,
    foreign_schema_name             text,
    foreign_table_name              text,
    foreign_attribute_name          text
);
alter table metamodel.link_logical_key_attribute
    add constraint link_logical_key_attribute_pk
        primary key (logical_key_rk, logical_attribute_rk, effective_from_dttm);
create unique index link_logical_key_attribute_nk
    on metamodel.link_logical_key_attribute (key_name, table_name, attribute_name, schema_name, effective_from_dttm);


create table metamodel.bridge_physical_table (
    physical_table_rk           bigint      default nextval('metamodel.md_seq'::regclass) not null,

    effective_from_dttm         timestamptz not null,
    effective_to_dttm           timestamptz not null,
    deleted_flg                 boolean     not null,
    version_rk                  bigint      not null,

    source_rk                   bigint      not null,
    data_layer_rk               smallint    not null,
    schema_name                 text        not null,
    table_name                  text        not null,
    table_desc                  text,
    table_type_rk               smallint    not null,
    table_type_cd               char        not null,
    is_dict_flg                 bool        not null,
    is_map_flg                  bool        not null,
    is_hub_flg                  bool        not null,
    is_sal_flg                  bool        not null,
    is_mart_flg                 bool        not null,
    is_bridge_flg               bool        not null,
    is_link_flg                 bool        not null,
    is_accessor_flg             bool        not null,
    table_distribution_type_rk  smallint,
    table_distribution_type_cd  char,
    version_scd_type_rk         smallint    not null,
    history_scd_type_rk         smallint    not null
);
alter table metamodel.bridge_physical_table
    add constraint bridge_physical_table_pk
        primary key (physical_table_rk, effective_from_dttm);
create unique index bridge_physical_table_nk
    on metamodel.bridge_physical_table (table_name, schema_name, effective_from_dttm);


create table metamodel.bridge_physical_attribute (
    physical_attribute_rk       bigint      default nextval('metamodel.md_seq'::regclass) not null,

    effective_from_dttm         timestamptz not null,
    effective_to_dttm           timestamptz not null,
    deleted_flg                 boolean     not null,
    version_rk                  bigint      not null,

    schema_name                 text        not null,
    table_name                  text        not null,
    physical_table_rk           bigint      not null,
    attribute_name              text        not null,
    attribute_desc              text,
    attribute_no                smallint    not null,
    data_type_id                smallint    not null,
    data_type_rk                smallint    not null,
    data_type_size_cnt          int,
    data_type_scale_cnt         int,
    is_nullable_flg             boolean     not null,
    default_value_txt           text,
    distribution_no             smallint,
    data_domain_rk	            smallint,
    metadata_type_rk            smallint
);
alter table metamodel.bridge_physical_attribute
    add constraint bridge_physical_attribute_pk
        primary key (physical_attribute_rk, effective_from_dttm);
create unique index bridge_physical_attribute_tk
    on metamodel.bridge_physical_attribute (attribute_name, physical_table_rk, effective_from_dttm);
create unique index bridge_physical_attribute_nk
    on metamodel.bridge_physical_attribute (attribute_name, table_name, schema_name, effective_from_dttm);


create table metamodel.bridge_physical_key (
    physical_key_rk                 bigint      default nextval('metamodel.md_seq'::regclass) not null,

    effective_from_dttm             timestamptz not null,
    effective_to_dttm               timestamptz not null,
    deleted_flg                     boolean     not null,
    version_rk                      bigint      not null,

    key_name                        text        not null,
    schema_name                     text        not null,
    physical_table_rk               bigint      not null,
    table_name                      text        not null
);
alter table metamodel.bridge_physical_key
    add constraint bridge_physical_key_pk
        primary key (physical_key_rk, effective_from_dttm);
create unique index bridge_physical_key_nk
        on metamodel.bridge_physical_key (key_name, schema_name, effective_from_dttm);


create table metamodel.link_physical_key_attribute (
    physical_key_rk                 bigint      not null,
    physical_attribute_rk           bigint      not null,

    effective_from_dttm             timestamptz not null,
    effective_to_dttm               timestamptz not null,
    deleted_flg                     boolean     not null,
    version_rk                      bigint      not null,

    schema_name                     text        not null,
    key_name                        text        not null,
    table_name                      text        not null,
    attribute_name                  text        not null,
    attribute_no                    smallint    not null
);
alter table metamodel.link_physical_key_attribute
    add constraint link_physical_key_attribute_pk
        primary key (physical_key_rk, physical_attribute_rk, effective_from_dttm);
create unique index link_physical_key_attribute_nk
    on metamodel.link_physical_key_attribute (key_name, table_name, attribute_name, schema_name, effective_from_dttm);


create table metamodel.bridge_table (
    table_rk                        bigint      default nextval('metamodel.md_seq'::regclass) not null,

    effective_from_dttm             timestamptz not null,
    effective_to_dttm               timestamptz not null,
    deleted_flg                     boolean     not null,
    version_rk                      bigint      not null,

    domain_rk                       smallint    not null,
    data_layer_rk                   smallint    not null,
    source_rk                       bigint      not null,
    schema_name                     text        not null,
    table_name                      text        not null,
    table_desc                      text,
    table_type_rk                   smallint    not null,
    table_distribution_type_rk      smallint    not null,
    is_dict_flg                     bool        not null,
    is_map_flg                      bool        not null,
    is_hub_flg                      bool        not null,
    is_sal_flg                      bool        not null,
    is_mart_flg                     bool        not null,
    is_bridge_flg                   bool        not null,
    is_link_flg                     bool        not null,
    is_temporal_flg                 bool        not null,
    is_accessor_flg                 bool        not null,
    version_scd_type_rk             smallint    not null,
    history_scd_type_rk             smallint    not null,
    model_major_version_from_num    smallint,
    model_minor_version_from_num    smallint,
    model_major_version_to_num      smallint,
    model_minor_version_to_num      smallint,
    is_deprecated_flg               bool        not null,
    model_effective_date	        date
);
alter table metamodel.bridge_table
    add constraint bridge_table_pk
        primary key (table_rk, effective_from_dttm);
create unique index bridge_table_nk
        on metamodel.bridge_table (table_name, schema_name, effective_from_dttm);


create table metamodel.bridge_attribute (
    attribute_rk                    bigint      default nextval('metamodel.md_seq'::regclass) not null,

    effective_from_dttm             timestamptz not null,
    effective_to_dttm               timestamptz not null,
    deleted_flg                     boolean     not null,
    version_rk                      bigint      not null,

    table_rk                        bigint      not null,
    attribute_name                  text        not null,
    attribute_no                    smallint    not null,
    attribute_desc                  text,
    data_type_rk                    smallint    not null,
    data_type_size_cnt              smallint,
    data_type_scale_cnt             smallint,
    is_nullable_flg                 bool        not null,
    default_value_txt               text,
    data_domain_rk                  smallint    not null,
    is_metadata_flg                 bool        not null,
    model_major_version_from_num    smallint,
    model_minor_version_from_num    smallint,
    model_major_version_to_num      smallint,
    model_minor_version_to_num      smallint,
    is_deprecated_flg               bool        not null,
    model_effective_date	        date
);
alter table metamodel.bridge_attribute
    add constraint bridge_attribute_pk
        primary key (attribute_rk, effective_from_dttm);
create unique index bridge_attribute_nk
        on metamodel.bridge_attribute (table_rk, attribute_name, effective_from_dttm);


create table metamodel.bridge_key (
    key_rk                          bigint      default nextval('metamodel.md_seq'::regclass) not null,

    effective_from_dttm             timestamptz not null,
    effective_to_dttm               timestamptz not null,
    deleted_flg                     boolean     not null,
    version_rk                      bigint      not null,

    key_name                        text        not null,
    table_rk                        bigint      not null,
    is_primary_flg                  bool        not null,
    is_foreign_flg                  bool        not null,
    is_unique_flg                   bool        not null,
    is_hash_flg                     bool        not null,
    is_distribution_flg             bool        not null,
    model_major_version_from_num    smallint,
    model_minor_version_from_num    smallint,
    model_major_version_to_num      smallint,
    model_minor_version_to_num      smallint,
    is_deprecated_flg               bool        not null,
    model_effective_date	        date
);
alter table metamodel.bridge_key
    add constraint bridge_key_pk
        primary key (key_rk, effective_from_dttm);
create unique index bridge_key_nk
        on metamodel.bridge_key (table_rk, key_name, effective_from_dttm);


create table metamodel.link_key_attribute (
    key_rk                          bigint      not null,
    attribute_rk                    bigint      not null,

    effective_from_dttm             timestamptz not null,
    effective_to_dttm               timestamptz not null,
    deleted_flg                     boolean     not null,
    version_rk                      bigint      not null,

    attribute_no                    smallint    not null,
    foreign_key_rk                  bigint,
    foreign_attribute_rk            bigint
);
alter table metamodel.link_key_attribute
    add constraint link_key_attribute_pk
        primary key (key_rk, attribute_rk, effective_from_dttm);


create table metamodel.link_table_schema_x_data_layer (
    schema_name     text        not null,
    data_layer_rk   smallint    not null,
    source_cd       text
);
alter table metamodel.link_table_schema_x_data_layer
    add constraint link_table_schema_x_data_layer_pk
        primary key (schema_name);
insert into metamodel.link_table_schema_x_data_layer (schema_name, data_layer_rk, source_cd) values ('rdv', 1, null);
insert into metamodel.link_table_schema_x_data_layer (schema_name, data_layer_rk, source_cd) values ('rdv_dict', 1, null);
insert into metamodel.link_table_schema_x_data_layer (schema_name, data_layer_rk, source_cd) values ('idl', 3, 'DTPL');
insert into metamodel.link_table_schema_x_data_layer (schema_name, data_layer_rk, source_cd) values ('bdm', 4, 'DTPL');



create table metamodel.link_table_data_vault_type_x_flg (
    data_layer_rk               smallint    not null,
    table_data_vault_type_cd    text        not null,
    is_dict_flg                 bool,
    is_map_flg                  bool,
    is_hub_flg                  bool,
    is_sal_flg                  bool,
    is_mart_flg                 bool,
    is_bridge_flg               bool,
    is_link_flg                 bool
);
alter table metamodel.link_table_data_vault_type_x_flg
    add constraint link_table_data_vault_type_x_flg_pk
        primary key (data_layer_rk, table_data_vault_type_cd);
insert into metamodel.link_table_data_vault_type_x_flg (data_layer_rk, table_data_vault_type_cd, is_dict_flg, is_map_flg, is_hub_flg, is_sal_flg, is_mart_flg, is_bridge_flg, is_link_flg) values (1, 'dict', true, false, false, false, false, false, false);
insert into metamodel.link_table_data_vault_type_x_flg (data_layer_rk, table_data_vault_type_cd, is_dict_flg, is_map_flg, is_hub_flg, is_sal_flg, is_mart_flg, is_bridge_flg, is_link_flg) values (1, 'ref', true, false, false, false, false, false, false);
insert into metamodel.link_table_data_vault_type_x_flg (data_layer_rk, table_data_vault_type_cd, is_dict_flg, is_map_flg, is_hub_flg, is_sal_flg, is_mart_flg, is_bridge_flg, is_link_flg) values (1, 'map', false, true, false, false, false, false, false);
insert into metamodel.link_table_data_vault_type_x_flg (data_layer_rk, table_data_vault_type_cd, is_dict_flg, is_map_flg, is_hub_flg, is_sal_flg, is_mart_flg, is_bridge_flg, is_link_flg) values (1, 'hub', false, false, true, false, false, false, false);
insert into metamodel.link_table_data_vault_type_x_flg (data_layer_rk, table_data_vault_type_cd, is_dict_flg, is_map_flg, is_hub_flg, is_sal_flg, is_mart_flg, is_bridge_flg, is_link_flg) values (1, 'sal', false, false, false, true, false, false, false);
insert into metamodel.link_table_data_vault_type_x_flg (data_layer_rk, table_data_vault_type_cd, is_dict_flg, is_map_flg, is_hub_flg, is_sal_flg, is_mart_flg, is_bridge_flg, is_link_flg) values (1, 'mart', false, false, false, false, true, false, false);
insert into metamodel.link_table_data_vault_type_x_flg (data_layer_rk, table_data_vault_type_cd, is_dict_flg, is_map_flg, is_hub_flg, is_sal_flg, is_mart_flg, is_bridge_flg, is_link_flg) values (1, 'lnk', false, false, false, false, false, false, true);
insert into metamodel.link_table_data_vault_type_x_flg (data_layer_rk, table_data_vault_type_cd, is_dict_flg, is_map_flg, is_hub_flg, is_sal_flg, is_mart_flg, is_bridge_flg, is_link_flg) values (1, 'rlnk', false, false, false, false, false, false, true);
insert into metamodel.link_table_data_vault_type_x_flg (data_layer_rk, table_data_vault_type_cd, is_dict_flg, is_map_flg, is_hub_flg, is_sal_flg, is_mart_flg, is_bridge_flg, is_link_flg) values (3, 'idl dictionary', true, false, false, false, false, false, false);
insert into metamodel.link_table_data_vault_type_x_flg (data_layer_rk, table_data_vault_type_cd, is_dict_flg, is_map_flg, is_hub_flg, is_sal_flg, is_mart_flg, is_bridge_flg, is_link_flg) values (3, 'idl hub', false, false, true, false, false, false, false);
insert into metamodel.link_table_data_vault_type_x_flg (data_layer_rk, table_data_vault_type_cd, is_dict_flg, is_map_flg, is_hub_flg, is_sal_flg, is_mart_flg, is_bridge_flg, is_link_flg) values (3, 'idl same-as-link', false, false, false, true, false, false, false);
insert into metamodel.link_table_data_vault_type_x_flg (data_layer_rk, table_data_vault_type_cd, is_dict_flg, is_map_flg, is_hub_flg, is_sal_flg, is_mart_flg, is_bridge_flg, is_link_flg) values (3, 'idl main table', false, false, false, false, false, true, false);
insert into metamodel.link_table_data_vault_type_x_flg (data_layer_rk, table_data_vault_type_cd, is_dict_flg, is_map_flg, is_hub_flg, is_sal_flg, is_mart_flg, is_bridge_flg, is_link_flg) values (3, 'idl bbridge', false, false, false, false, false, true, false);
insert into metamodel.link_table_data_vault_type_x_flg (data_layer_rk, table_data_vault_type_cd, is_dict_flg, is_map_flg, is_hub_flg, is_sal_flg, is_mart_flg, is_bridge_flg, is_link_flg) values (3, 'idl tbridge', false, false, false, false, false, true, false);
insert into metamodel.link_table_data_vault_type_x_flg (data_layer_rk, table_data_vault_type_cd, is_dict_flg, is_map_flg, is_hub_flg, is_sal_flg, is_mart_flg, is_bridge_flg, is_link_flg) values (3, 'idl relation', false, false, false, false, false, false, true);
insert into metamodel.link_table_data_vault_type_x_flg (data_layer_rk, table_data_vault_type_cd, is_dict_flg, is_map_flg, is_hub_flg, is_sal_flg, is_mart_flg, is_bridge_flg, is_link_flg) values (3, 'idl blink', false, false, false, false, false, false, true);
insert into metamodel.link_table_data_vault_type_x_flg (data_layer_rk, table_data_vault_type_cd, is_dict_flg, is_map_flg, is_hub_flg, is_sal_flg, is_mart_flg, is_bridge_flg, is_link_flg) values (3, 'idl tlink', false, false, false, false, false, false, true);
insert into metamodel.link_table_data_vault_type_x_flg (data_layer_rk, table_data_vault_type_cd, is_dict_flg, is_map_flg, is_hub_flg, is_sal_flg, is_mart_flg, is_bridge_flg, is_link_flg) values (4, 'bdm dictionary', true, false, false, false, false, false, false);
insert into metamodel.link_table_data_vault_type_x_flg (data_layer_rk, table_data_vault_type_cd, is_dict_flg, is_map_flg, is_hub_flg, is_sal_flg, is_mart_flg, is_bridge_flg, is_link_flg) values (4, 'bdm main table', false, false, false, false, false, true, false);
insert into metamodel.link_table_data_vault_type_x_flg (data_layer_rk, table_data_vault_type_cd, is_dict_flg, is_map_flg, is_hub_flg, is_sal_flg, is_mart_flg, is_bridge_flg, is_link_flg) values (4, 'bdm main table snap', false, false, false, false, false, true, false);
insert into metamodel.link_table_data_vault_type_x_flg (data_layer_rk, table_data_vault_type_cd, is_dict_flg, is_map_flg, is_hub_flg, is_sal_flg, is_mart_flg, is_bridge_flg, is_link_flg) values (4, 'bdm main table interval', false, false, false, false, false, true, false);
insert into metamodel.link_table_data_vault_type_x_flg (data_layer_rk, table_data_vault_type_cd, is_dict_flg, is_map_flg, is_hub_flg, is_sal_flg, is_mart_flg, is_bridge_flg, is_link_flg) values (4, 'bdm relation', false, false, false, false, false, false, true);
insert into metamodel.link_table_data_vault_type_x_flg (data_layer_rk, table_data_vault_type_cd, is_dict_flg, is_map_flg, is_hub_flg, is_sal_flg, is_mart_flg, is_bridge_flg, is_link_flg) values (4, 'bdm relation interval', false, false, false, false, false, false, true);
insert into metamodel.link_table_data_vault_type_x_flg (data_layer_rk, table_data_vault_type_cd, is_dict_flg, is_map_flg, is_hub_flg, is_sal_flg, is_mart_flg, is_bridge_flg, is_link_flg) values (4, 'bdm relation snap', false, false, false, false, false, false, true);


create table metamodel.link_table_name_x_flg (
    data_layer_rk       smallint    not null,
    table_pattern_cd    text        not null,
    is_table_prefix_flg bool        not null,
    priority_no         smallint    not null,
    is_dict_flg         bool        not null,
    is_map_flg          bool        not null,
    is_hub_flg          bool        not null,
    is_sal_flg          bool        not null,
    is_mart_flg         bool        not null,
    is_bridge_flg       bool        not null,
    is_link_flg         bool        not null,
    is_accessor_flg     bool        not null
);
alter table metamodel.link_table_name_x_flg
    add constraint link_table_name_x_flg_pk
        primary key (data_layer_rk, is_table_prefix_flg, table_pattern_cd);

insert into metamodel.link_table_name_x_flg (data_layer_rk, table_pattern_cd, is_table_prefix_flg, priority_no, is_dict_flg, is_map_flg, is_hub_flg, is_sal_flg, is_mart_flg, is_bridge_flg, is_link_flg, is_accessor_flg) values (1, 'dict_',            true, 1, true,  false, false, false, false, false, false, false);
insert into metamodel.link_table_name_x_flg (data_layer_rk, table_pattern_cd, is_table_prefix_flg, priority_no, is_dict_flg, is_map_flg, is_hub_flg, is_sal_flg, is_mart_flg, is_bridge_flg, is_link_flg, is_accessor_flg) values (1, 'v_dict_',          true, 1, true,  false, false, false, false, false, false, true);
insert into metamodel.link_table_name_x_flg (data_layer_rk, table_pattern_cd, is_table_prefix_flg, priority_no, is_dict_flg, is_map_flg, is_hub_flg, is_sal_flg, is_mart_flg, is_bridge_flg, is_link_flg, is_accessor_flg) values (1, 'v_sn_all_dict_',   true, 1, true,  false, false, false, false, false, false, true);
insert into metamodel.link_table_name_x_flg (data_layer_rk, table_pattern_cd, is_table_prefix_flg, priority_no, is_dict_flg, is_map_flg, is_hub_flg, is_sal_flg, is_mart_flg, is_bridge_flg, is_link_flg, is_accessor_flg) values (1, 'v_sn_hash_dict_',  true, 1, true,  false, false, false, false, false, false, true);
insert into metamodel.link_table_name_x_flg (data_layer_rk, table_pattern_cd, is_table_prefix_flg, priority_no, is_dict_flg, is_map_flg, is_hub_flg, is_sal_flg, is_mart_flg, is_bridge_flg, is_link_flg, is_accessor_flg) values (1, 'v_sn_vld_dict_',   true, 1, true,  false, false, false, false, false, false, true);
insert into metamodel.link_table_name_x_flg (data_layer_rk, table_pattern_cd, is_table_prefix_flg, priority_no, is_dict_flg, is_map_flg, is_hub_flg, is_sal_flg, is_mart_flg, is_bridge_flg, is_link_flg, is_accessor_flg) values (1, 'v_sv_dict_',       true, 1, true,  false, false, false, false, false, false, true);
insert into metamodel.link_table_name_x_flg (data_layer_rk, table_pattern_cd, is_table_prefix_flg, priority_no, is_dict_flg, is_map_flg, is_hub_flg, is_sal_flg, is_mart_flg, is_bridge_flg, is_link_flg, is_accessor_flg) values (1, 'hmart_',           true, 1, false, false, false, false, true,  false, false, false);
insert into metamodel.link_table_name_x_flg (data_layer_rk, table_pattern_cd, is_table_prefix_flg, priority_no, is_dict_flg, is_map_flg, is_hub_flg, is_sal_flg, is_mart_flg, is_bridge_flg, is_link_flg, is_accessor_flg) values (1, 'v_hmart_',         true, 1, false, false, false, false, true,  false, false, true);
insert into metamodel.link_table_name_x_flg (data_layer_rk, table_pattern_cd, is_table_prefix_flg, priority_no, is_dict_flg, is_map_flg, is_hub_flg, is_sal_flg, is_mart_flg, is_bridge_flg, is_link_flg, is_accessor_flg) values (1, 'v_sn_all_hmart_',  true, 1, false, false, false, false, true,  false, false, true);
insert into metamodel.link_table_name_x_flg (data_layer_rk, table_pattern_cd, is_table_prefix_flg, priority_no, is_dict_flg, is_map_flg, is_hub_flg, is_sal_flg, is_mart_flg, is_bridge_flg, is_link_flg, is_accessor_flg) values (1, 'v_sn_hash_hmart_', true, 1, false, false, false, false, true,  false, false, true);
insert into metamodel.link_table_name_x_flg (data_layer_rk, table_pattern_cd, is_table_prefix_flg, priority_no, is_dict_flg, is_map_flg, is_hub_flg, is_sal_flg, is_mart_flg, is_bridge_flg, is_link_flg, is_accessor_flg) values (1, 'v_sn_vld_hmart_',  true, 1, false, false, false, false, true,  false, false, true);
insert into metamodel.link_table_name_x_flg (data_layer_rk, table_pattern_cd, is_table_prefix_flg, priority_no, is_dict_flg, is_map_flg, is_hub_flg, is_sal_flg, is_mart_flg, is_bridge_flg, is_link_flg, is_accessor_flg) values (1, 'v_sv_hmart_',      true, 1, false, false, false, false, true,  false, false, true);
insert into metamodel.link_table_name_x_flg (data_layer_rk, table_pattern_cd, is_table_prefix_flg, priority_no, is_dict_flg, is_map_flg, is_hub_flg, is_sal_flg, is_mart_flg, is_bridge_flg, is_link_flg, is_accessor_flg) values (1, 'hub_',             true, 1, false, false, true,  false, false, false, false, false);
insert into metamodel.link_table_name_x_flg (data_layer_rk, table_pattern_cd, is_table_prefix_flg, priority_no, is_dict_flg, is_map_flg, is_hub_flg, is_sal_flg, is_mart_flg, is_bridge_flg, is_link_flg, is_accessor_flg) values (1, 'v_hub_',           true, 1, false, false, true,  false, false, false, false, true);
insert into metamodel.link_table_name_x_flg (data_layer_rk, table_pattern_cd, is_table_prefix_flg, priority_no, is_dict_flg, is_map_flg, is_hub_flg, is_sal_flg, is_mart_flg, is_bridge_flg, is_link_flg, is_accessor_flg) values (1, 'v_sn_all_hub_',    true, 1, false, false, true,  false, false, false, false, true);
insert into metamodel.link_table_name_x_flg (data_layer_rk, table_pattern_cd, is_table_prefix_flg, priority_no, is_dict_flg, is_map_flg, is_hub_flg, is_sal_flg, is_mart_flg, is_bridge_flg, is_link_flg, is_accessor_flg) values (1, 'v_sn_hash_hub_',   true, 1, false, false, true,  false, false, false, false, true);
insert into metamodel.link_table_name_x_flg (data_layer_rk, table_pattern_cd, is_table_prefix_flg, priority_no, is_dict_flg, is_map_flg, is_hub_flg, is_sal_flg, is_mart_flg, is_bridge_flg, is_link_flg, is_accessor_flg) values (1, 'v_sn_vld_hub_',    true, 1, false, false, true,  false, false, false, false, true);
insert into metamodel.link_table_name_x_flg (data_layer_rk, table_pattern_cd, is_table_prefix_flg, priority_no, is_dict_flg, is_map_flg, is_hub_flg, is_sal_flg, is_mart_flg, is_bridge_flg, is_link_flg, is_accessor_flg) values (1, 'v_sv_hub_',        true, 1, false, false, true,  false, false, false, false, true);
insert into metamodel.link_table_name_x_flg (data_layer_rk, table_pattern_cd, is_table_prefix_flg, priority_no, is_dict_flg, is_map_flg, is_hub_flg, is_sal_flg, is_mart_flg, is_bridge_flg, is_link_flg, is_accessor_flg) values (1, 'lnk_',             true, 1, false, false, false, false, false, false, true,  false);
insert into metamodel.link_table_name_x_flg (data_layer_rk, table_pattern_cd, is_table_prefix_flg, priority_no, is_dict_flg, is_map_flg, is_hub_flg, is_sal_flg, is_mart_flg, is_bridge_flg, is_link_flg, is_accessor_flg) values (1, 'v_lnk_',           true, 1, false, false, false, false, false, false, true,  true);
insert into metamodel.link_table_name_x_flg (data_layer_rk, table_pattern_cd, is_table_prefix_flg, priority_no, is_dict_flg, is_map_flg, is_hub_flg, is_sal_flg, is_mart_flg, is_bridge_flg, is_link_flg, is_accessor_flg) values (1, 'v_sn_all_lnk_',    true, 1, false, false, false, false, false, false, true,  true);
insert into metamodel.link_table_name_x_flg (data_layer_rk, table_pattern_cd, is_table_prefix_flg, priority_no, is_dict_flg, is_map_flg, is_hub_flg, is_sal_flg, is_mart_flg, is_bridge_flg, is_link_flg, is_accessor_flg) values (1, 'v_sn_hash_lnk_',   true, 1, false, false, false, false, false, false, true,  true);
insert into metamodel.link_table_name_x_flg (data_layer_rk, table_pattern_cd, is_table_prefix_flg, priority_no, is_dict_flg, is_map_flg, is_hub_flg, is_sal_flg, is_mart_flg, is_bridge_flg, is_link_flg, is_accessor_flg) values (1, 'v_sn_vld_lnk_',    true, 1, false, false, false, false, false, false, true,  true);
insert into metamodel.link_table_name_x_flg (data_layer_rk, table_pattern_cd, is_table_prefix_flg, priority_no, is_dict_flg, is_map_flg, is_hub_flg, is_sal_flg, is_mart_flg, is_bridge_flg, is_link_flg, is_accessor_flg) values (1, 'v_sv_lnk_',        true, 1, false, false, false, false, false, false, true,  true);
insert into metamodel.link_table_name_x_flg (data_layer_rk, table_pattern_cd, is_table_prefix_flg, priority_no, is_dict_flg, is_map_flg, is_hub_flg, is_sal_flg, is_mart_flg, is_bridge_flg, is_link_flg, is_accessor_flg) values (1, 'map_',             true, 1, false, true,  false, false, false, false, false, false);
insert into metamodel.link_table_name_x_flg (data_layer_rk, table_pattern_cd, is_table_prefix_flg, priority_no, is_dict_flg, is_map_flg, is_hub_flg, is_sal_flg, is_mart_flg, is_bridge_flg, is_link_flg, is_accessor_flg) values (1, 'v_map_',           true, 1, false, true,  false, false, false, false, false, true);
insert into metamodel.link_table_name_x_flg (data_layer_rk, table_pattern_cd, is_table_prefix_flg, priority_no, is_dict_flg, is_map_flg, is_hub_flg, is_sal_flg, is_mart_flg, is_bridge_flg, is_link_flg, is_accessor_flg) values (1, 'v_sn_all_map_',    true, 1, false, true,  false, false, false, false, false, true);
insert into metamodel.link_table_name_x_flg (data_layer_rk, table_pattern_cd, is_table_prefix_flg, priority_no, is_dict_flg, is_map_flg, is_hub_flg, is_sal_flg, is_mart_flg, is_bridge_flg, is_link_flg, is_accessor_flg) values (1, 'v_sn_hash_map_',   true, 1, false, true,  false, false, false, false, false, true);
insert into metamodel.link_table_name_x_flg (data_layer_rk, table_pattern_cd, is_table_prefix_flg, priority_no, is_dict_flg, is_map_flg, is_hub_flg, is_sal_flg, is_mart_flg, is_bridge_flg, is_link_flg, is_accessor_flg) values (1, 'v_sn_vld_map_',    true, 1, false, true,  false, false, false, false, false, true);
insert into metamodel.link_table_name_x_flg (data_layer_rk, table_pattern_cd, is_table_prefix_flg, priority_no, is_dict_flg, is_map_flg, is_hub_flg, is_sal_flg, is_mart_flg, is_bridge_flg, is_link_flg, is_accessor_flg) values (1, 'v_sv_map_',        true, 1, false, true,  false, false, false, false, false, true);
insert into metamodel.link_table_name_x_flg (data_layer_rk, table_pattern_cd, is_table_prefix_flg, priority_no, is_dict_flg, is_map_flg, is_hub_flg, is_sal_flg, is_mart_flg, is_bridge_flg, is_link_flg, is_accessor_flg) values (1, 'mart_',            true, 1, false, false, false, false, true,  false, false, false);
insert into metamodel.link_table_name_x_flg (data_layer_rk, table_pattern_cd, is_table_prefix_flg, priority_no, is_dict_flg, is_map_flg, is_hub_flg, is_sal_flg, is_mart_flg, is_bridge_flg, is_link_flg, is_accessor_flg) values (1, 'v_mart_',          true, 1, false, false, false, false, true,  false, false, true);
insert into metamodel.link_table_name_x_flg (data_layer_rk, table_pattern_cd, is_table_prefix_flg, priority_no, is_dict_flg, is_map_flg, is_hub_flg, is_sal_flg, is_mart_flg, is_bridge_flg, is_link_flg, is_accessor_flg) values (1, 'v_sn_all_mart_',   true, 1, false, false, false, false, true,  false, false, true);
insert into metamodel.link_table_name_x_flg (data_layer_rk, table_pattern_cd, is_table_prefix_flg, priority_no, is_dict_flg, is_map_flg, is_hub_flg, is_sal_flg, is_mart_flg, is_bridge_flg, is_link_flg, is_accessor_flg) values (1, 'v_sn_hash_mart_',  true, 1, false, false, false, false, true,  false, false, true);
insert into metamodel.link_table_name_x_flg (data_layer_rk, table_pattern_cd, is_table_prefix_flg, priority_no, is_dict_flg, is_map_flg, is_hub_flg, is_sal_flg, is_mart_flg, is_bridge_flg, is_link_flg, is_accessor_flg) values (1, 'v_sn_vld_mart_',   true, 1, false, false, false, false, true,  false, false, true);
insert into metamodel.link_table_name_x_flg (data_layer_rk, table_pattern_cd, is_table_prefix_flg, priority_no, is_dict_flg, is_map_flg, is_hub_flg, is_sal_flg, is_mart_flg, is_bridge_flg, is_link_flg, is_accessor_flg) values (1, 'v_sv_mart_',       true, 1, false, false, false, false, true,  false, false, true);
insert into metamodel.link_table_name_x_flg (data_layer_rk, table_pattern_cd, is_table_prefix_flg, priority_no, is_dict_flg, is_map_flg, is_hub_flg, is_sal_flg, is_mart_flg, is_bridge_flg, is_link_flg, is_accessor_flg) values (1, 'rlnk_',            true, 1, false, false, false, false, false, false, true,  false);
insert into metamodel.link_table_name_x_flg (data_layer_rk, table_pattern_cd, is_table_prefix_flg, priority_no, is_dict_flg, is_map_flg, is_hub_flg, is_sal_flg, is_mart_flg, is_bridge_flg, is_link_flg, is_accessor_flg) values (1, 'v_rlnk_',          true, 1, false, false, false, false, false, false, true,  true);
insert into metamodel.link_table_name_x_flg (data_layer_rk, table_pattern_cd, is_table_prefix_flg, priority_no, is_dict_flg, is_map_flg, is_hub_flg, is_sal_flg, is_mart_flg, is_bridge_flg, is_link_flg, is_accessor_flg) values (1, 'v_sn_all_rlnk_',   true, 1, false, false, false, false, false, false, true,  true);
insert into metamodel.link_table_name_x_flg (data_layer_rk, table_pattern_cd, is_table_prefix_flg, priority_no, is_dict_flg, is_map_flg, is_hub_flg, is_sal_flg, is_mart_flg, is_bridge_flg, is_link_flg, is_accessor_flg) values (1, 'v_sn_hash_rlnk_',  true, 1, false, false, false, false, false, false, true,  true);
insert into metamodel.link_table_name_x_flg (data_layer_rk, table_pattern_cd, is_table_prefix_flg, priority_no, is_dict_flg, is_map_flg, is_hub_flg, is_sal_flg, is_mart_flg, is_bridge_flg, is_link_flg, is_accessor_flg) values (1, 'v_sn_vld_rlnk_',   true, 1, false, false, false, false, false, false, true,  true);
insert into metamodel.link_table_name_x_flg (data_layer_rk, table_pattern_cd, is_table_prefix_flg, priority_no, is_dict_flg, is_map_flg, is_hub_flg, is_sal_flg, is_mart_flg, is_bridge_flg, is_link_flg, is_accessor_flg) values (1, 'v_sv_rlnk_',       true, 1, false, false, false, false, false, false, true,  true);
insert into metamodel.link_table_name_x_flg (data_layer_rk, table_pattern_cd, is_table_prefix_flg, priority_no, is_dict_flg, is_map_flg, is_hub_flg, is_sal_flg, is_mart_flg, is_bridge_flg, is_link_flg, is_accessor_flg) values (1, 'ref_',             true, 1, true,  false, false, false, false, false, false, false);
insert into metamodel.link_table_name_x_flg (data_layer_rk, table_pattern_cd, is_table_prefix_flg, priority_no, is_dict_flg, is_map_flg, is_hub_flg, is_sal_flg, is_mart_flg, is_bridge_flg, is_link_flg, is_accessor_flg) values (1, 'v_ref_',           true, 1, true,  false, false, false, false, false, false, true);
insert into metamodel.link_table_name_x_flg (data_layer_rk, table_pattern_cd, is_table_prefix_flg, priority_no, is_dict_flg, is_map_flg, is_hub_flg, is_sal_flg, is_mart_flg, is_bridge_flg, is_link_flg, is_accessor_flg) values (1, 'v_sn_all_ref_',    true, 1, true,  false, false, false, false, false, false, true);
insert into metamodel.link_table_name_x_flg (data_layer_rk, table_pattern_cd, is_table_prefix_flg, priority_no, is_dict_flg, is_map_flg, is_hub_flg, is_sal_flg, is_mart_flg, is_bridge_flg, is_link_flg, is_accessor_flg) values (1, 'v_sn_hash_ref_',   true, 1, true,  false, false, false, false, false, false, true);
insert into metamodel.link_table_name_x_flg (data_layer_rk, table_pattern_cd, is_table_prefix_flg, priority_no, is_dict_flg, is_map_flg, is_hub_flg, is_sal_flg, is_mart_flg, is_bridge_flg, is_link_flg, is_accessor_flg) values (1, 'v_sn_vld_ref_',    true, 1, true,  false, false, false, false, false, false, true);
insert into metamodel.link_table_name_x_flg (data_layer_rk, table_pattern_cd, is_table_prefix_flg, priority_no, is_dict_flg, is_map_flg, is_hub_flg, is_sal_flg, is_mart_flg, is_bridge_flg, is_link_flg, is_accessor_flg) values (1, 'v_sv_ref_',        true, 1, true,  false, false, false, false, false, false, true);
insert into metamodel.link_table_name_x_flg (data_layer_rk, table_pattern_cd, is_table_prefix_flg, priority_no, is_dict_flg, is_map_flg, is_hub_flg, is_sal_flg, is_mart_flg, is_bridge_flg, is_link_flg, is_accessor_flg) values (1, 'sal_',             true, 1, false, false, false, true,  false, false, false, false);
insert into metamodel.link_table_name_x_flg (data_layer_rk, table_pattern_cd, is_table_prefix_flg, priority_no, is_dict_flg, is_map_flg, is_hub_flg, is_sal_flg, is_mart_flg, is_bridge_flg, is_link_flg, is_accessor_flg) values (1, 'v_sal_',           true, 1, false, false, false, true,  false, false, false, true);
insert into metamodel.link_table_name_x_flg (data_layer_rk, table_pattern_cd, is_table_prefix_flg, priority_no, is_dict_flg, is_map_flg, is_hub_flg, is_sal_flg, is_mart_flg, is_bridge_flg, is_link_flg, is_accessor_flg) values (1, 'v_sn_all_sal_',    true, 1, false, false, false, true,  false, false, false, true);
insert into metamodel.link_table_name_x_flg (data_layer_rk, table_pattern_cd, is_table_prefix_flg, priority_no, is_dict_flg, is_map_flg, is_hub_flg, is_sal_flg, is_mart_flg, is_bridge_flg, is_link_flg, is_accessor_flg) values (1, 'v_sn_hash_sal_',   true, 1, false, false, false, true,  false, false, false, true);
insert into metamodel.link_table_name_x_flg (data_layer_rk, table_pattern_cd, is_table_prefix_flg, priority_no, is_dict_flg, is_map_flg, is_hub_flg, is_sal_flg, is_mart_flg, is_bridge_flg, is_link_flg, is_accessor_flg) values (1, 'v_sn_vld_sal_',    true, 1, false, false, false, true,  false, false, false, true);
insert into metamodel.link_table_name_x_flg (data_layer_rk, table_pattern_cd, is_table_prefix_flg, priority_no, is_dict_flg, is_map_flg, is_hub_flg, is_sal_flg, is_mart_flg, is_bridge_flg, is_link_flg, is_accessor_flg) values (1, 'v_sv_sal_',        true, 1, false, false, false, true,  false, false, false, true);
insert into metamodel.link_table_name_x_flg (data_layer_rk, table_pattern_cd, is_table_prefix_flg, priority_no, is_dict_flg, is_map_flg, is_hub_flg, is_sal_flg, is_mart_flg, is_bridge_flg, is_link_flg, is_accessor_flg) values (1, 'v_sn_all_',        true, 1, false, false, false, false, false, false, false, true);
insert into metamodel.link_table_name_x_flg (data_layer_rk, table_pattern_cd, is_table_prefix_flg, priority_no, is_dict_flg, is_map_flg, is_hub_flg, is_sal_flg, is_mart_flg, is_bridge_flg, is_link_flg, is_accessor_flg) values (1, 'v_sn_hash_',       true, 1, false, false, false, false, false, false, false, true);
insert into metamodel.link_table_name_x_flg (data_layer_rk, table_pattern_cd, is_table_prefix_flg, priority_no, is_dict_flg, is_map_flg, is_hub_flg, is_sal_flg, is_mart_flg, is_bridge_flg, is_link_flg, is_accessor_flg) values (1, 'v_sn_vld_',        true, 1, false, false, false, false, false, false, false, true);
insert into metamodel.link_table_name_x_flg (data_layer_rk, table_pattern_cd, is_table_prefix_flg, priority_no, is_dict_flg, is_map_flg, is_hub_flg, is_sal_flg, is_mart_flg, is_bridge_flg, is_link_flg, is_accessor_flg) values (1, 'v_sv_',            true, 1, false, false, false, false, false, false, false, true);
insert into metamodel.link_table_name_x_flg (data_layer_rk, table_pattern_cd, is_table_prefix_flg, priority_no, is_dict_flg, is_map_flg, is_hub_flg, is_sal_flg, is_mart_flg, is_bridge_flg, is_link_flg, is_accessor_flg) values (1, 'v_',               true, 1, false, false, false, false, false, false, false, true);

insert into metamodel.link_table_name_x_flg (data_layer_rk, table_pattern_cd, is_table_prefix_flg, priority_no, is_dict_flg, is_map_flg, is_hub_flg, is_sal_flg, is_mart_flg, is_bridge_flg, is_link_flg, is_accessor_flg) values (3, 'dict_', true, 1, true, false, false, false, false, false, false, false);
insert into metamodel.link_table_name_x_flg (data_layer_rk, table_pattern_cd, is_table_prefix_flg, priority_no, is_dict_flg, is_map_flg, is_hub_flg, is_sal_flg, is_mart_flg, is_bridge_flg, is_link_flg, is_accessor_flg) values (3, 'v_sn_all_dict_', true, 1, true, false, false, false, false, false, false, true);
insert into metamodel.link_table_name_x_flg (data_layer_rk, table_pattern_cd, is_table_prefix_flg, priority_no, is_dict_flg, is_map_flg, is_hub_flg, is_sal_flg, is_mart_flg, is_bridge_flg, is_link_flg, is_accessor_flg) values (3, 'hub_', true, 1, false, false, true, false, false, false, false, false);
insert into metamodel.link_table_name_x_flg (data_layer_rk, table_pattern_cd, is_table_prefix_flg, priority_no, is_dict_flg, is_map_flg, is_hub_flg, is_sal_flg, is_mart_flg, is_bridge_flg, is_link_flg, is_accessor_flg) values (3, 'v_sn_all_hub_', true, 1, false, false, true, false, false, false, false, true);
insert into metamodel.link_table_name_x_flg (data_layer_rk, table_pattern_cd, is_table_prefix_flg, priority_no, is_dict_flg, is_map_flg, is_hub_flg, is_sal_flg, is_mart_flg, is_bridge_flg, is_link_flg, is_accessor_flg) values (3, 'sal_', true, 1, false, false, false, true, false, false, false, false);
insert into metamodel.link_table_name_x_flg (data_layer_rk, table_pattern_cd, is_table_prefix_flg, priority_no, is_dict_flg, is_map_flg, is_hub_flg, is_sal_flg, is_mart_flg, is_bridge_flg, is_link_flg, is_accessor_flg) values (3, 'v_sn_all_sal_', true, 1, false, false, false, true, false, false, false, true);
insert into metamodel.link_table_name_x_flg (data_layer_rk, table_pattern_cd, is_table_prefix_flg, priority_no, is_dict_flg, is_map_flg, is_hub_flg, is_sal_flg, is_mart_flg, is_bridge_flg, is_link_flg, is_accessor_flg) values (3, 'bbridge_', true, 1, false, false, false, false, false, true, false, false);
insert into metamodel.link_table_name_x_flg (data_layer_rk, table_pattern_cd, is_table_prefix_flg, priority_no, is_dict_flg, is_map_flg, is_hub_flg, is_sal_flg, is_mart_flg, is_bridge_flg, is_link_flg, is_accessor_flg) values (3, 'v_sn_all_bbridge_', true, 1, false, false, false, false, false, true, false, true);
insert into metamodel.link_table_name_x_flg (data_layer_rk, table_pattern_cd, is_table_prefix_flg, priority_no, is_dict_flg, is_map_flg, is_hub_flg, is_sal_flg, is_mart_flg, is_bridge_flg, is_link_flg, is_accessor_flg) values (3, 'tbridge_', true, 1, false, false, false, false, false, true, false, false);
insert into metamodel.link_table_name_x_flg (data_layer_rk, table_pattern_cd, is_table_prefix_flg, priority_no, is_dict_flg, is_map_flg, is_hub_flg, is_sal_flg, is_mart_flg, is_bridge_flg, is_link_flg, is_accessor_flg) values (3, 'v_sn_all_tbridge_', true, 1, false, false, false, false, false, true, false, true);
insert into metamodel.link_table_name_x_flg (data_layer_rk, table_pattern_cd, is_table_prefix_flg, priority_no, is_dict_flg, is_map_flg, is_hub_flg, is_sal_flg, is_mart_flg, is_bridge_flg, is_link_flg, is_accessor_flg) values (3, 'blink_', true, 1, false, false, false, false, false, false, true, false);
insert into metamodel.link_table_name_x_flg (data_layer_rk, table_pattern_cd, is_table_prefix_flg, priority_no, is_dict_flg, is_map_flg, is_hub_flg, is_sal_flg, is_mart_flg, is_bridge_flg, is_link_flg, is_accessor_flg) values (3, 'v_sn_all_blink_', true, 1, false, false, false, false, false, false, true, true);
insert into metamodel.link_table_name_x_flg (data_layer_rk, table_pattern_cd, is_table_prefix_flg, priority_no, is_dict_flg, is_map_flg, is_hub_flg, is_sal_flg, is_mart_flg, is_bridge_flg, is_link_flg, is_accessor_flg) values (3, 'tlink_', true, 1, false, false, false, false, false, false, true, false);
insert into metamodel.link_table_name_x_flg (data_layer_rk, table_pattern_cd, is_table_prefix_flg, priority_no, is_dict_flg, is_map_flg, is_hub_flg, is_sal_flg, is_mart_flg, is_bridge_flg, is_link_flg, is_accessor_flg) values (3, 'v_sn_all_tlink_', true, 1, false, false, false, false, false, false, true, true);
insert into metamodel.link_table_name_x_flg (data_layer_rk, table_pattern_cd, is_table_prefix_flg, priority_no, is_dict_flg, is_map_flg, is_hub_flg, is_sal_flg, is_mart_flg, is_bridge_flg, is_link_flg, is_accessor_flg) values (3, 'v_sn_all_', true, 1, false, false, false, false, false, false, false, true);
insert into metamodel.link_table_name_x_flg (data_layer_rk, table_pattern_cd, is_table_prefix_flg, priority_no, is_dict_flg, is_map_flg, is_hub_flg, is_sal_flg, is_mart_flg, is_bridge_flg, is_link_flg, is_accessor_flg) values (3, 'v_sn_hash_', true, 1, false, false, false, false, false, false, false, true);
insert into metamodel.link_table_name_x_flg (data_layer_rk, table_pattern_cd, is_table_prefix_flg, priority_no, is_dict_flg, is_map_flg, is_hub_flg, is_sal_flg, is_mart_flg, is_bridge_flg, is_link_flg, is_accessor_flg) values (4, 'dict_', true, 1, true, false, false, false, false, false, false, false);
insert into metamodel.link_table_name_x_flg (data_layer_rk, table_pattern_cd, is_table_prefix_flg, priority_no, is_dict_flg, is_map_flg, is_hub_flg, is_sal_flg, is_mart_flg, is_bridge_flg, is_link_flg, is_accessor_flg) values (4, 'v_sn_all_dict_', true, 1, true, false, false, false, false, false, false, true);
insert into metamodel.link_table_name_x_flg (data_layer_rk, table_pattern_cd, is_table_prefix_flg, priority_no, is_dict_flg, is_map_flg, is_hub_flg, is_sal_flg, is_mart_flg, is_bridge_flg, is_link_flg, is_accessor_flg) values (4, 'r_dict_', true, 1, true, false, false, false, false, false, false, true);
insert into metamodel.link_table_name_x_flg (data_layer_rk, table_pattern_cd, is_table_prefix_flg, priority_no, is_dict_flg, is_map_flg, is_hub_flg, is_sal_flg, is_mart_flg, is_bridge_flg, is_link_flg, is_accessor_flg) values (4, 'hub_', true, 1, false, false, true, false, false, false, false, false);
insert into metamodel.link_table_name_x_flg (data_layer_rk, table_pattern_cd, is_table_prefix_flg, priority_no, is_dict_flg, is_map_flg, is_hub_flg, is_sal_flg, is_mart_flg, is_bridge_flg, is_link_flg, is_accessor_flg) values (4, 'v_sn_all_hub_', true, 1, false, false, true, false, false, false, false, true);
insert into metamodel.link_table_name_x_flg (data_layer_rk, table_pattern_cd, is_table_prefix_flg, priority_no, is_dict_flg, is_map_flg, is_hub_flg, is_sal_flg, is_mart_flg, is_bridge_flg, is_link_flg, is_accessor_flg) values (4, 'r_hub_', true, 1, false, false, true, false, false, false, false, true);
insert into metamodel.link_table_name_x_flg (data_layer_rk, table_pattern_cd, is_table_prefix_flg, priority_no, is_dict_flg, is_map_flg, is_hub_flg, is_sal_flg, is_mart_flg, is_bridge_flg, is_link_flg, is_accessor_flg) values (4, 'sal_', true, 1, false, false, false, true, false, false, false, false);
insert into metamodel.link_table_name_x_flg (data_layer_rk, table_pattern_cd, is_table_prefix_flg, priority_no, is_dict_flg, is_map_flg, is_hub_flg, is_sal_flg, is_mart_flg, is_bridge_flg, is_link_flg, is_accessor_flg) values (4, 'v_sn_all_sal_', true, 1, false, false, false, true, false, false, false, true);
insert into metamodel.link_table_name_x_flg (data_layer_rk, table_pattern_cd, is_table_prefix_flg, priority_no, is_dict_flg, is_map_flg, is_hub_flg, is_sal_flg, is_mart_flg, is_bridge_flg, is_link_flg, is_accessor_flg) values (4, 'r_sal_', true, 1, false, false, false, true, false, false, false, true);
insert into metamodel.link_table_name_x_flg (data_layer_rk, table_pattern_cd, is_table_prefix_flg, priority_no, is_dict_flg, is_map_flg, is_hub_flg, is_sal_flg, is_mart_flg, is_bridge_flg, is_link_flg, is_accessor_flg) values (4, '%\_x\_%', false, 2, false, false, false, false, false, false, true, false);
insert into metamodel.link_table_name_x_flg (data_layer_rk, table_pattern_cd, is_table_prefix_flg, priority_no, is_dict_flg, is_map_flg, is_hub_flg, is_sal_flg, is_mart_flg, is_bridge_flg, is_link_flg, is_accessor_flg) values (4, 'v\_sn\_all\_%\_x\_%', false, 1, false, false, false, false, false, false, true, true);
insert into metamodel.link_table_name_x_flg (data_layer_rk, table_pattern_cd, is_table_prefix_flg, priority_no, is_dict_flg, is_map_flg, is_hub_flg, is_sal_flg, is_mart_flg, is_bridge_flg, is_link_flg, is_accessor_flg) values (4, 'r\_%\_x\_%', false, 1, false, false, false, false, false, false, true, true);
insert into metamodel.link_table_name_x_flg (data_layer_rk, table_pattern_cd, is_table_prefix_flg, priority_no, is_dict_flg, is_map_flg, is_hub_flg, is_sal_flg, is_mart_flg, is_bridge_flg, is_link_flg, is_accessor_flg) values (4, 'v_sn_all_', true, 3, false, false, false, false, false, false, false, true);
insert into metamodel.link_table_name_x_flg (data_layer_rk, table_pattern_cd, is_table_prefix_flg, priority_no, is_dict_flg, is_map_flg, is_hub_flg, is_sal_flg, is_mart_flg, is_bridge_flg, is_link_flg, is_accessor_flg) values (4, 'r_', true, 3, false, false, false, false, false, false, false, true);


create table metamodel.link_attribute_postfix_x_data_domain (
    attribute_postfix_cd    text        not null,
    data_type_rk            smallint    not null,
    data_domain_rk          bigint      not null
);
alter table metamodel.link_attribute_postfix_x_data_domain
    add constraint link_attribute_postfix_x_data_domain_pk
        primary key (attribute_postfix_cd, data_type_rk);
insert into metamodel.link_attribute_postfix_x_data_domain (attribute_postfix_cd, data_type_rk, data_domain_rk) values ('%\_rk', 2, 14);
insert into metamodel.link_attribute_postfix_x_data_domain (attribute_postfix_cd, data_type_rk, data_domain_rk) values ('%\_rk', 3, 13);
insert into metamodel.link_attribute_postfix_x_data_domain (attribute_postfix_cd, data_type_rk, data_domain_rk) values ('%\_rk', 4, 13);
insert into metamodel.link_attribute_postfix_x_data_domain (attribute_postfix_cd, data_type_rk, data_domain_rk) values ('%\_sk', 4, 15);
insert into metamodel.link_attribute_postfix_x_data_domain (attribute_postfix_cd, data_type_rk, data_domain_rk) values ('%\_code', 16, 16);
insert into metamodel.link_attribute_postfix_x_data_domain (attribute_postfix_cd, data_type_rk, data_domain_rk) values ('%\_cd', 16, 17);
insert into metamodel.link_attribute_postfix_x_data_domain (attribute_postfix_cd, data_type_rk, data_domain_rk) values ('%\_num', 16, 18);
insert into metamodel.link_attribute_postfix_x_data_domain (attribute_postfix_cd, data_type_rk, data_domain_rk) values ('%\_no', 2, 20);
insert into metamodel.link_attribute_postfix_x_data_domain (attribute_postfix_cd, data_type_rk, data_domain_rk) values ('%\_id', 16, 21);
insert into metamodel.link_attribute_postfix_x_data_domain (attribute_postfix_cd, data_type_rk, data_domain_rk) values ('%\_bk', 16, 22);
insert into metamodel.link_attribute_postfix_x_data_domain (attribute_postfix_cd, data_type_rk, data_domain_rk) values ('%\_name', 16, 23);
insert into metamodel.link_attribute_postfix_x_data_domain (attribute_postfix_cd, data_type_rk, data_domain_rk) values ('%\_desc', 16, 24);
insert into metamodel.link_attribute_postfix_x_data_domain (attribute_postfix_cd, data_type_rk, data_domain_rk) values ('%\_txt', 16, 25);
insert into metamodel.link_attribute_postfix_x_data_domain (attribute_postfix_cd, data_type_rk, data_domain_rk) values ('%\_flg', 1, 26);
insert into metamodel.link_attribute_postfix_x_data_domain (attribute_postfix_cd, data_type_rk, data_domain_rk) values ('%\_date', 8, 27);
insert into metamodel.link_attribute_postfix_x_data_domain (attribute_postfix_cd, data_type_rk, data_domain_rk) values ('%\_dttm', 9, 28);
insert into metamodel.link_attribute_postfix_x_data_domain (attribute_postfix_cd, data_type_rk, data_domain_rk) values ('%\_sum', 5, 29);
insert into metamodel.link_attribute_postfix_x_data_domain (attribute_postfix_cd, data_type_rk, data_domain_rk) values ('%\_cnt', 5, 30);
insert into metamodel.link_attribute_postfix_x_data_domain (attribute_postfix_cd, data_type_rk, data_domain_rk) values ('%\_pc', 5, 31);
insert into metamodel.link_attribute_postfix_x_data_domain (attribute_postfix_cd, data_type_rk, data_domain_rk) values ('%\_rate', 5, 32);
insert into metamodel.link_attribute_postfix_x_data_domain (attribute_postfix_cd, data_type_rk, data_domain_rk) values ('%\_score', 5, 33);
insert into metamodel.link_attribute_postfix_x_data_domain (attribute_postfix_cd, data_type_rk, data_domain_rk) values ('%\_uid', 17, 34);
insert into metamodel.link_attribute_postfix_x_data_domain (attribute_postfix_cd, data_type_rk, data_domain_rk) values ('%\_url', 16, 35);
insert into metamodel.link_attribute_postfix_x_data_domain (attribute_postfix_cd, data_type_rk, data_domain_rk) values ('%\_duration', 13, 37);
insert into metamodel.link_attribute_postfix_x_data_domain (attribute_postfix_cd, data_type_rk, data_domain_rk) values ('%\_duration', 4, 38);
insert into metamodel.link_attribute_postfix_x_data_domain (attribute_postfix_cd, data_type_rk, data_domain_rk) values ('%\_duration', 16, 36);
insert into metamodel.link_attribute_postfix_x_data_domain (attribute_postfix_cd, data_type_rk, data_domain_rk) values ('%\_json', 18, 39);
insert into metamodel.link_attribute_postfix_x_data_domain (attribute_postfix_cd, data_type_rk, data_domain_rk) values ('%\_json', 19, 39);
insert into metamodel.link_attribute_postfix_x_data_domain (attribute_postfix_cd, data_type_rk, data_domain_rk) values ('%\_regexp', 16, 40);
insert into metamodel.link_attribute_postfix_x_data_domain (attribute_postfix_cd, data_type_rk, data_domain_rk) values ('%\_version', 16, 41);
insert into metamodel.link_attribute_postfix_x_data_domain (attribute_postfix_cd, data_type_rk, data_domain_rk) values ('%\_tbi', 4, 42);


create table metamodel.link_attribute_name_x_metadata_type (
    attribute_name      text    not null,
    metadata_type_rk    bigint  not null,
    data_domain_rk      bigint
);
alter table metamodel.link_attribute_name_x_metadata_type
    add constraint link_attribute_name_x_metadata_type_pk
        primary key (attribute_name);
insert into metamodel.link_attribute_name_x_metadata_type (attribute_name, metadata_type_rk, data_domain_rk) values ('hash_diff', 0, 7);
insert into metamodel.link_attribute_name_x_metadata_type (attribute_name, metadata_type_rk, data_domain_rk) values ('version_id', 1, 2);
insert into metamodel.link_attribute_name_x_metadata_type (attribute_name, metadata_type_rk, data_domain_rk) values ('to_version_id', 2, 2);
insert into metamodel.link_attribute_name_x_metadata_type (attribute_name, metadata_type_rk, data_domain_rk) values ('effective_date', 3, 4);
insert into metamodel.link_attribute_name_x_metadata_type (attribute_name, metadata_type_rk, data_domain_rk) values ('effective_dttm', 3, 10);
insert into metamodel.link_attribute_name_x_metadata_type (attribute_name, metadata_type_rk, data_domain_rk) values ('effective_from_date', 3, 5);
insert into metamodel.link_attribute_name_x_metadata_type (attribute_name, metadata_type_rk, data_domain_rk) values ('effective_from_dttm', 3, 8);
insert into metamodel.link_attribute_name_x_metadata_type (attribute_name, metadata_type_rk, data_domain_rk) values ('effective_to_date', 4, 6);
insert into metamodel.link_attribute_name_x_metadata_type (attribute_name, metadata_type_rk, data_domain_rk) values ('effective_to_dttm', 4, 9);
insert into metamodel.link_attribute_name_x_metadata_type (attribute_name, metadata_type_rk, data_domain_rk) values ('deleted_flg', 5, 3);
insert into metamodel.link_attribute_name_x_metadata_type (attribute_name, metadata_type_rk, data_domain_rk) values ('valid_flg', 6, null);
insert into metamodel.link_attribute_name_x_metadata_type (attribute_name, metadata_type_rk, data_domain_rk) values ('invalid_id', 7, 1);
insert into metamodel.link_attribute_name_x_metadata_type (attribute_name, metadata_type_rk, data_domain_rk) values ('src_cd', 8, null);
insert into metamodel.link_attribute_name_x_metadata_type (attribute_name, metadata_type_rk, data_domain_rk) values ('bk_src_cd', 9, null);
insert into metamodel.link_attribute_name_x_metadata_type (attribute_name, metadata_type_rk, data_domain_rk) values ('bk_id', 10, null);
insert into metamodel.link_attribute_name_x_metadata_type (attribute_name, metadata_type_rk, data_domain_rk) values ('bk_type', 11, 11);
insert into metamodel.link_attribute_name_x_metadata_type (attribute_name, metadata_type_rk, data_domain_rk) values ('synth_code', 12, null);


create table metamodel.link_attribute_name_x_scd_type (
    attribute_name      text    not null,
    version_scd_type_rk bigint,
    history_scd_type_rk bigint
);
alter table metamodel.link_attribute_name_x_scd_type
    add constraint link_attribute_name_x_scd_type_pk
        primary key (attribute_name);
insert into metamodel.link_attribute_name_x_scd_type (attribute_name, version_scd_type_rk, history_scd_type_rk) values ('version_id', 2, null);
insert into metamodel.link_attribute_name_x_scd_type (attribute_name, version_scd_type_rk, history_scd_type_rk) values ('to_version_id', 3, null);
insert into metamodel.link_attribute_name_x_scd_type (attribute_name, version_scd_type_rk, history_scd_type_rk) values ('effective_date', null, 2);
insert into metamodel.link_attribute_name_x_scd_type (attribute_name, version_scd_type_rk, history_scd_type_rk) values ('effective_dttm', null, 2);
insert into metamodel.link_attribute_name_x_scd_type (attribute_name, version_scd_type_rk, history_scd_type_rk) values ('effective_from_date', null, 2);
insert into metamodel.link_attribute_name_x_scd_type (attribute_name, version_scd_type_rk, history_scd_type_rk) values ('effective_from_dttm', null, 2);
insert into metamodel.link_attribute_name_x_scd_type (attribute_name, version_scd_type_rk, history_scd_type_rk) values ('effective_to_date', null, 3);
insert into metamodel.link_attribute_name_x_scd_type (attribute_name, version_scd_type_rk, history_scd_type_rk) values ('effective_to_dttm', null, 3);


create table metamodel.link_data_domain_x_scd_type (
    data_domain_cd      text    not null,
    version_scd_type_rk bigint,
    history_scd_type_rk bigint
);
alter table metamodel.link_data_domain_x_scd_type
    add constraint link_data_domain_x_scd_type_pk
        primary key (data_domain_cd);
insert into metamodel.link_data_domain_x_scd_type (data_domain_cd, version_scd_type_rk, history_scd_type_rk) values ('dversionidentificator', 2, null);
insert into metamodel.link_data_domain_x_scd_type (data_domain_cd, version_scd_type_rk, history_scd_type_rk) values ('dtoversionidentificator', 3, null);
insert into metamodel.link_data_domain_x_scd_type (data_domain_cd, version_scd_type_rk, history_scd_type_rk) values ('deffectivedate', null, 2);
insert into metamodel.link_data_domain_x_scd_type (data_domain_cd, version_scd_type_rk, history_scd_type_rk) values ('deffectivedttm', null, 2);
insert into metamodel.link_data_domain_x_scd_type (data_domain_cd, version_scd_type_rk, history_scd_type_rk) values ('deffectivebegindate', null, 2);
insert into metamodel.link_data_domain_x_scd_type (data_domain_cd, version_scd_type_rk, history_scd_type_rk) values ('deffectivebegindttm', null, 2);
insert into metamodel.link_data_domain_x_scd_type (data_domain_cd, version_scd_type_rk, history_scd_type_rk) values ('deffectiveenddate', null, 3);
insert into metamodel.link_data_domain_x_scd_type (data_domain_cd, version_scd_type_rk, history_scd_type_rk) values ('deffectiveenddttm', null, 3);


create table metamodel.link_physical_data_type (
    physical_data_type_id   smallint    not null,
    data_type_rk            smallint    not null
);
alter table metamodel.link_physical_data_type
    add constraint link_physical_data_type_pk
        primary key (physical_data_type_id);
insert into metamodel.link_physical_data_type (physical_data_type_id, data_type_rk) values (16, 1);
insert into metamodel.link_physical_data_type (physical_data_type_id, data_type_rk) values (21, 2);
insert into metamodel.link_physical_data_type (physical_data_type_id, data_type_rk) values (23, 3);
insert into metamodel.link_physical_data_type (physical_data_type_id, data_type_rk) values (20, 4);
insert into metamodel.link_physical_data_type (physical_data_type_id, data_type_rk) values (1700, 5);
insert into metamodel.link_physical_data_type (physical_data_type_id, data_type_rk) values (700, 6);
insert into metamodel.link_physical_data_type (physical_data_type_id, data_type_rk) values (701, 7);
insert into metamodel.link_physical_data_type (physical_data_type_id, data_type_rk) values (1042, 14);
insert into metamodel.link_physical_data_type (physical_data_type_id, data_type_rk) values (1043, 15);
insert into metamodel.link_physical_data_type (physical_data_type_id, data_type_rk) values (25, 16);
insert into metamodel.link_physical_data_type (physical_data_type_id, data_type_rk) values (1082, 8);
insert into metamodel.link_physical_data_type (physical_data_type_id, data_type_rk) values (1114, 9);
insert into metamodel.link_physical_data_type (physical_data_type_id, data_type_rk) values (1184, 10);
insert into metamodel.link_physical_data_type (physical_data_type_id, data_type_rk) values (1083, 11);
insert into metamodel.link_physical_data_type (physical_data_type_id, data_type_rk) values (1266, 12);
insert into metamodel.link_physical_data_type (physical_data_type_id, data_type_rk) values (1186, 13);
insert into metamodel.link_physical_data_type (physical_data_type_id, data_type_rk) values (2950, 17);
insert into metamodel.link_physical_data_type (physical_data_type_id, data_type_rk) values (114, 18);
insert into metamodel.link_physical_data_type (physical_data_type_id, data_type_rk) values (3802, 19);


create table metamodel.link_physical_table_type (
    physical_table_type_cd  char        not null,
    table_type_rk           smallint    not null
);
alter table metamodel.link_physical_table_type
    add constraint link_physical_table_type_pk
        primary key (physical_table_type_cd);
insert into metamodel.link_physical_table_type (physical_table_type_cd, table_type_rk) values ('r', 1);
insert into metamodel.link_physical_table_type (physical_table_type_cd, table_type_rk) values ('v', 2);


create table metamodel.link_physical_table_distribution_type (
    physical_table_distribution_type_cd   char        not null,
    table_distribution_type_rk            smallint    not null
);
alter table metamodel.link_physical_table_distribution_type
    add constraint link_physical_distribution_type_pk
        primary key (physical_table_distribution_type_cd);
insert into metamodel.link_physical_table_distribution_type (physical_table_distribution_type_cd, table_distribution_type_rk) values ('p', 1);
insert into metamodel.link_physical_table_distribution_type (physical_table_distribution_type_cd, table_distribution_type_rk) values ('r', 2);


create table metamodel.bridge_service (
	service_rk          bigint      not null default nextval('metamodel.md_seq'::regclass),
	service_cd          dict.dname  not null,
	service_name        dict.dname,
	service_desc        text,
	service_host_name   text        not null,
	service_url         text,
	service_alt_url     text,
	service_type_rk     smallint    not null,
	environment_rk      smallint    not null,
	effective_from_dttm timestamptz not null,
	effective_to_dttm   timestamptz not null,
	deleted_flg         boolean     not null,
	constraint bridge_service_pk primary key (service_rk, effective_from_dttm)
);


create table metamodel.link_service_delivery (
	service_rk       bigint   not null,
	code_delivery_rk smallint not null,
	constraint link_service_delivery_pk primary key (service_rk, code_delivery_rk)
);


create table metamodel.link_service_schema (
     service_rk         bigint      not null,
     schema_name        dict.dname  not null,
     code_delivery_rk   smallint    not null,
     data_layer_rk      smallint    not null
);
alter table metamodel.link_service_schema
    add constraint link_service_schema_pk
        primary key (service_rk, schema_name);


create table metamodel.link_resource_source (
    resource_rk bigint not null,
    source_rk bigint not null,
    effective_from_dttm timestamptz not null,
    effective_to_dttm timestamptz not null,
    deleted_flg boolean not null,
    constraint link_resource_source_pk
        primary key (resource_rk, source_rk, effective_to_dttm)
);


create table metamodel.link_resource_table (
    resource_rk bigint not null,
    table_rk bigint not null,
    effective_from_dttm timestamptz not null,
    effective_to_dttm timestamptz not null,
    deleted_flg boolean not null,
    constraint link_resource_table_pk
        primary key (resource_rk, table_rk, effective_to_dttm)
);


create table metamodel.bridge_etl_algorithm (
    etl_algorithm_rk    bigint      not null,
    etl_algorithm_cd    text        not null,
    code_delivery_rk    smallint    not null,

    effective_from_dttm timestamptz not null,
    effective_to_dttm   timestamptz not null,
    deleted_flg         boolean     not null,
    version_rk          bigint      not null,

    constraint bridge_etl_algorithm_pk primary key (
        etl_algorithm_rk,
        effective_from_dttm
    )
);
create index bridge_etl_algorithm_uk
    on metamodel.bridge_etl_algorithm (
        etl_algorithm_cd
      , effective_from_dttm
    );


create table metamodel.link_flow_resource (
    flow_rk                     bigint      not null,
    resource_rk                 bigint      not null,
    flow_resource_link_type_rk  smallint    not null,
    etl_algorithm_rk            bigint      not null,
    table_rk                    bigint      not null,
    code_delivery_rk            smallint    not null,

    effective_from_dttm         timestamptz not null,
    effective_to_dttm           timestamptz not null,
    deleted_flg                 boolean     not null,
    version_rk                  bigint      not null,

    constraint link_flow_resource_pk primary key (
        resource_rk,
        flow_rk,
        flow_resource_link_type_rk,
        etl_algorithm_rk,
        table_rk,
        effective_from_dttm
    )
);
