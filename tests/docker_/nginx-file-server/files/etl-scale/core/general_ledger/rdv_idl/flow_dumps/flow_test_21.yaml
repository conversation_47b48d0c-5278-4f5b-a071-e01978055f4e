schema_version: 2.0
flows:
  - id: flow_test_21
    metadata:
      - name: src_version_id
        default: 223
      - name: common_version_id
        default: 123
      - name: init_load_flg
        datatype: bool
        default: false
      - name: algorithm_name
        default: test_alg_test_table_21
    tasks:
      - id: update_resource_state_test_table_2
        type: ceh_core_idl.app.operators.services.reg_delta_operator
        properties:
          target_resource_name: ceh.test_schema_1.test_table_2
          source_resource_names:
            - ceh.test_schema_1.test_table_2
          target_table_names:
            - test_schema_2.test_table_2
          algorithm_name: ${algorithm_name}
