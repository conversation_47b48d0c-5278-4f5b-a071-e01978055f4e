import itertools
import re
import time
from typing import Callable, List, Literal, Optional, Tuple, Union
from uuid import UUID

import requests
from helpers import get_container_logs
from metaloader_rest_api import models, schemas
from metaloader_rest_api.common_model import LAST_DATE
from metaloader_rest_api.common_repository import SessionResource
from metaloader_rest_api.master_files_schemas import (
    ControlFlow as MasterFlow,
)
from metaloader_rest_api.master_files_schemas import (
    FlowGroup,
)
from metaloader_rest_api.master_files_schemas import (
    TriggerFlow as FlowStep,
)
from pytest import fail, fixture, mark
from sqlalchemy import text
from sqlalchemy.orm import Session

DUMMY_RELEASE_METADATA = {
    "release_num": "51.0.2",
    "release_desc": "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut "
    "labore et dolore magna aliqua.",
    "release_date": "1970-01-01T00:00:00Z",
    "effective_from_date": "1970-01-01T00:00:00Z",
    "release_module": "core",
    "git_revision_hash": "foobar42",
}


@mark.skip(reason="Включить, когда выкатим аутентификацию")
@mark.parametrize(
    "method, endpoint",
    [
        ("POST", "/v1/tx"),
        ("POST", "/v1/tx/0000/commit"),
        ("POST", "/v1/tx/0000/try"),
        ("POST", "/v1/tx/0000/rollback"),
        ("GET", "/v1/tx/0000"),
        ("GET", "/v1/releases/dummy_module"),
        ("GET", "/v1/master_flows/names/dummy_module"),
        ("POST", "/v1/master_flows/actions/added"),
        ("POST", "/v1/master_flows/actions/modified"),
        ("POST", "/v1/master_flows/actions/renamed"),
        ("POST", "/v1/master_flows/actions/deleted"),
        ("POST", "/v1/flows/load_from_af"),
        ("POST", "/v1/sources/load_from_xlsx"),
        ("POST", "/v1/modules/load_from_xlsx"),
    ],
)
def test_requires_authentication(endpoint, method, test_client, monkeypatch):
    with monkeypatch.context() as m:
        m.delitem(test_client.headers, "Authorization")
        response = test_client.request(method, endpoint)
        assert response.status_code == 401

    with monkeypatch.context() as m:
        m.setitem(test_client.headers, "Authorization", "invalidtoken")
        response = test_client.request(method, endpoint)
        assert response.status_code == 401


@mark.parametrize(
    "endpoint_suffix, expected_transaction_status",
    [
        ("commit_async", schemas.TransactionStatus.COMMITTED.value),
        ("try_async", schemas.TransactionStatus.ROLLEDBACK.value),
    ],
)
def test_process_transaction_async_happy_path(
    endpoint_suffix,
    expected_transaction_status,
    test_client,
    db_session: Session,
    master_flow,
    truncate_tables,
    truncate_service,
    cleanup_redis,
    core_module,
):
    response = test_client.post(
        "/v1/tx", json={"release_metadata": DUMMY_RELEASE_METADATA}
    )
    assert response.status_code == 200
    tx_id = response.json()

    response = test_client.post(
        "/v1/master_flows/actions/added",
        json={
            "tx_uid": tx_id,
            "name": master_flow.master_flow_name,
            "definition": master_flow.model_dump(mode="json", exclude_unset=True),
        },
    )
    assert response.status_code == 200

    response = test_client.post(
        f"/v1/tx/{tx_id}/{endpoint_suffix}",
        json={
            "mode": "incremental",
            "expected_master_flows": [master_flow.master_flow_name],
        },
    )
    assert response.status_code == 202
    assert "Location" in response.headers

    for times_tried in itertools.count(start=1):
        time.sleep(0.5)
        response = test_client.get(f"/v1/tx/{tx_id}")
        assert response.status_code == 200
        try:
            assert response.json()["status"] == expected_transaction_status
            break
        except AssertionError:
            if times_tried < 10:
                pass
            else:
                raise

    if endpoint_suffix == "try_async":
        return

    n_flows, n_flow_links = db_session.execute(
        text(
            """
            SELECT
                (SELECT COUNT(*) FROM metamodel.bridge_flow),
                (SELECT COUNT(*) FROM metamodel.link_flow)
            """
        )
    ).one()
    assert n_flows == 4
    assert n_flow_links == 3


@mark.parametrize("dry_run", [True, False])
@mark.parametrize(
    "transaction_status",
    [
        models.TransactionStatus.IN_PROGRESS,
        models.TransactionStatus.COMMITTED,
        models.TransactionStatus.ROLLEDBACK,
        models.TransactionStatus.ETL_ERROR,
    ],
)
def test_refuses_to_process_non_opened_transaction(
    dry_run,
    transaction_status,
    test_client,
    db_session,
    celery_container,
    send_celery_task__process_tx,
    truncate_service,
    cleanup_redis,
):
    # Создаем транзакцию с кастомным статусом
    response = test_client.post(
        "/v1/tx", json={"release_metadata": DUMMY_RELEASE_METADATA}
    )
    tx_uid = UUID(response.json())
    Tx = models.MasterFlowTransaction  # noqa
    db_session.execute(
        text(
            f"""
            UPDATE {Tx.__table_args__['schema']}.{Tx.__tablename__}
            SET {Tx.status.name} = {transaction_status}
            WHERE {Tx.id.name} = '{tx_uid}';
            COMMIT;
            """
        )
    )

    # Отправляем задачу и ждем пока она завершится
    send_celery_task__process_tx(str(tx_uid), dry_run)
    time.sleep(0.5)

    # Проверяем по логам, что транзакцию обрабатывать отказались
    logs = celery_container.logs(stdout=True, stderr=True, tail=10)
    assert (
        b"Attempting to work on transaction that has already been started processing"
        in logs
    )


@mark.parametrize(
    "endpoint_suffix, task_name",
    [
        ("commit_async", "run_release_sources_integration_etl"),
        ("try_async", "dry_run_release_sources_integration_etl"),
    ],
)
def test_refuses_to_process_transaction_second_time(
    endpoint_suffix,
    task_name,
    test_client,
    send_celery_task__process_tx,
    celery_container,
    master_flow,
    truncate_tables,
    truncate_service,
    core_module,
    cleanup_redis,
):
    # Создаем транзакцию и шлём задачу на её коммит два раза
    response = test_client.post(
        "/v1/tx", json={"release_metadata": DUMMY_RELEASE_METADATA}
    )
    tx_id = str(UUID(response.json()))  # чтобы упасть, если вернулся не UUID
    response = test_client.post(
        "/v1/master_flows/actions/added",
        json={
            "tx_uid": tx_id,
            "name": master_flow.master_flow_name,
            "definition": master_flow.model_dump(mode="json", exclude_unset=True),
        },
    )
    assert response.status_code == 200
    response = test_client.post(
        f"/v1/tx/{tx_id}/{endpoint_suffix}",
        json={
            "mode": "incremental",
            "expected_master_flows": [master_flow.master_flow_name],
        },
    )
    assert response.status_code == 202
    send_celery_task__process_tx(
        tx_id, dry_run=task_name == "dry_run_release_sources_integration_etl"
    )
    time.sleep(1)

    # Берём все строки логов (с опцией `tail` они почему-то возвращались не все)
    last_logs = get_container_logs(celery_container, tail=50)
    last_logs_reversed = reversed(last_logs)

    # Проверяем по логам, что транзакцию сначала обработали, а затем повторно обрабатывать отказались
    for log_line in last_logs_reversed:
        if (
            "Attempting to work on transaction that has already been started processing"
            in log_line
        ):
            break
    else:
        fail(
            f'"Transaction already started processing" message not found in logs\n{last_logs}'
        )

    for log_line in last_logs_reversed:
        if re.search(rf"Task {task_name}\[[a-z0-9-]+] succeeded in", log_line):
            break
    else:
        fail(f'"Task succeeded" message not found in logs\n{last_logs}')


@mark.parametrize(
    "endpoint_suffix",
    ["commit_async", "try_async"],
)
def test_process_transaction_async_etl_error_path(
    endpoint_suffix,
    test_client,
    monkeypatch,
    truncate_tables,
    truncate_service,
    cleanup_redis,
):
    monkeypatch.setitem(DUMMY_RELEASE_METADATA, "release_module", "NO-SUCH-MODULE")
    response = test_client.post(
        "/v1/tx", json={"release_metadata": DUMMY_RELEASE_METADATA}
    )
    assert response.status_code == 200
    tx_id = response.json()

    response = test_client.post(
        f"/v1/tx/{tx_id}/{endpoint_suffix}",
        json={"mode": "incremental", "expected_master_flows": []},
    )
    assert response.status_code == 202
    assert "Location" in response.headers

    resolved_statuses = [
        schemas.TransactionStatus.COMMITTED.value,
        schemas.TransactionStatus.ROLLEDBACK.value,
        schemas.TransactionStatus.ETL_ERROR.value,
    ]
    for _ in range(10):
        time.sleep(0.5)
        response = test_client.get(f"/v1/tx/{tx_id}")
        assert response.status_code == 200
        if response.json()["status"] in resolved_statuses:
            break
    else:
        assert response.json()["status"] == schemas.TransactionStatus.ETL_ERROR.value

    assert response.json()["result"]["master_flow_errors"] == {
        "error": ["Unknown ETL error"]
    }


@mark.parametrize(
    "endpoint_entity, endpoint_suffix",
    [
        ("master_flows", "added"),
        ("master_flows", "modified"),
        ("master_flows", "renamed"),
        ("master_flows", "deleted"),
        ("resources", "added"),
        ("resources", "modified"),
        ("resources", "deleted"),
    ],
)
def test_cant_add_actions_to_rolleback_transaction(
    test_client,
    endpoint_entity,
    endpoint_suffix,
    get_master_flow_action_payload,
    get_resource_action_payload,
    truncate_service,
    db_session: Session,
):
    create_tx = (
        "/v1/tx" if endpoint_entity == "master_flows" else f"/v1/{endpoint_entity}/tx"
    )
    response = test_client.post(
        create_tx, json={"release_metadata": DUMMY_RELEASE_METADATA}
    )
    assert response.status_code == 200
    tx_uid = response.json()

    assert (
        response := test_client.post(url=f"{create_tx}/{tx_uid}/rollback")
    ).status_code == 200, response.text

    response = test_client.post(
        url=f"/v1/{endpoint_entity}/actions/{endpoint_suffix}",
        json=get_master_flow_action_payload(tx_uid)
        if endpoint_entity == "master_flows"
        else get_resource_action_payload(tx_uid, action=endpoint_suffix),
    )
    payload = response.json()
    assert response.status_code == 400, payload
    assert payload["detail"] == "Attempting to modify a transaction that is not open"


# noinspection DuplicatedCode
@mark.parametrize(
    "endpoint_entity, endpoint_suffix",
    [
        ("master_flows", "added"),
        ("master_flows", "modified"),
        ("master_flows", "renamed"),
        ("master_flows", "deleted"),
        ("resources", "added"),
        ("resources", "modified"),
        ("resources", "deleted"),
    ],
)
def test_cant_add_actions_to_transaction_in_progress(
    endpoint_suffix,
    endpoint_entity,
    get_master_flow_action_payload,
    get_resource_action_payload,
    test_client,
    truncate_service,
    db_session: Session,
):
    create_tx = (
        "/v1/tx" if endpoint_entity == "master_flows" else f"/v1/{endpoint_entity}/tx"
    )
    response = test_client.post(
        create_tx, json={"release_metadata": DUMMY_RELEASE_METADATA}
    )
    assert response.status_code == 200
    tx_uid = response.json()

    Tx = models.MasterFlowTransaction  # noqa
    db_session.execute(
        text(
            f"UPDATE {Tx.__table_args__['schema']}.{Tx.__tablename__}"
            f" SET {Tx.status.name} = {models.TransactionStatus.IN_PROGRESS.value}"
            f" WHERE {Tx.id.name} = '{tx_uid}';"
            f"COMMIT;"
        )
    )

    response = test_client.post(
        url=f"/v1/{endpoint_entity}/actions/{endpoint_suffix}",
        json=get_master_flow_action_payload(tx_uid)
        if endpoint_entity == "master_flows"
        else get_resource_action_payload(tx_uid, action=endpoint_suffix),
    )
    payload = response.json()
    assert response.status_code == 400, payload
    assert payload["detail"] == "Attempting to modify a transaction that is not open"


# noinspection DuplicatedCode
@mark.parametrize(
    "endpoint_suffix",
    ["commit", "try", "rollback"],
)
def test_cant_resolve_transaction_in_progress(
    endpoint_suffix,
    test_client,
    master_flow,
    truncate_service,
    db_session: Session,
):
    response = test_client.post(
        "/v1/tx", json={"release_metadata": DUMMY_RELEASE_METADATA}
    )
    assert response.status_code == 200
    tx_uid = response.json()

    Tx = models.MasterFlowTransaction  # noqa
    db_session.execute(
        text(
            f"UPDATE {Tx.__table_args__['schema']}.{Tx.__tablename__}"
            f" SET {Tx.status.name} = {models.TransactionStatus.IN_PROGRESS.value}"
            f" WHERE {Tx.id.name} = '{tx_uid}';"
            f"COMMIT;"
        )
    )

    response = test_client.post(
        url=f"/v1/tx/{tx_uid}/{endpoint_suffix}",
        json={
            "mode": "incremental",
            "expected_master_flows": [],
        },
    )
    payload = response.json()
    assert response.status_code == 400, payload
    assert payload["detail"] == "Attempting to modify a transaction that is not open"


@mark.parametrize(
    "endpoint_suffix, etl_func_name",
    [
        ("commit", "run_release_sources_integration_etl"),
        ("try", "dry_run_release_sources_integration_etl"),
    ],
)
def test_commit_and_try_transition_transaction_to_in_progress(
    endpoint_suffix,
    etl_func_name,
    test_client,
    truncate_service,
    db_session: Session,
    monkeypatch,
):
    def capture_tx_status_halfway(*args, **kwargs):
        nonlocal tx_status_halfway
        Tx = models.MasterFlowTransaction  # noqa
        tx_status_halfway = db_session.scalar(
            text(
                f"SELECT {Tx.status.name}"
                f" FROM {Tx.__table_args__['schema']}.{Tx.__tablename__}"
                f" WHERE {Tx.id.name} = '{tx_uid}'"
            )
        )

    monkeypatch.setattr(
        f"metaloader_rest_api.app.{etl_func_name}",
        capture_tx_status_halfway,
    )

    response = test_client.post(
        "/v1/tx", json={"release_metadata": DUMMY_RELEASE_METADATA}
    )
    assert response.status_code == 200
    tx_uid = response.json()

    tx_status_halfway = None
    test_client.post(
        url=f"/v1/tx/{tx_uid}/{endpoint_suffix}",
        json={
            "mode": "incremental",
            "expected_master_flows": [],
        },
    )
    assert tx_status_halfway == models.TransactionStatus.IN_PROGRESS.value


@mark.parametrize(
    "endpoint_entity, endpoint_suffix",
    [
        ("master_flows", "added"),
        ("master_flows", "modified"),
        ("master_flows", "renamed"),
        ("master_flows", "deleted"),
        ("resources", "added"),
        ("resources", "modified"),
        ("resources", "deleted"),
    ],
)
def test_cant_add_actions_to_nonexistent_transaction(
    test_client,
    endpoint_suffix,
    endpoint_entity,
    get_master_flow_action_payload,
    get_resource_action_payload,
    truncate_service,
    db_session: Session,
):
    tx_uid = "00000000-0000-0000-0000-000000000000"
    response = test_client.post(
        url=f"/v1/{endpoint_entity}/actions/{endpoint_suffix}",
        json=get_master_flow_action_payload(tx_uid)
        if endpoint_entity == "master_flows"
        else get_resource_action_payload(tx_uid, action=endpoint_suffix),
    )
    payload = response.json()
    assert response.status_code == 404, payload
    assert payload["detail"] == "Transaction not found"


@mark.parametrize(
    "endpoint_entity, endpoint_suffix, http_409_msg_suffix",
    [
        ("master_flows", "added", "master flow"),
        ("master_flows", "modified", "master flow"),
        ("master_flows", "renamed", "master flow"),
        ("master_flows", "deleted", "master flow"),
        ("resources", "added", "resource"),
        ("resources", "modified", "resource"),
        ("resources", "deleted", "resource"),
    ],
)
def test_single_action_per_entity(
    test_client,
    endpoint_entity,
    endpoint_suffix,
    http_409_msg_suffix,
    get_resource_action_payload,
    get_master_flow_action_payload,
    recreate_schema_stg,
    truncate_service,
    truncate_tables,
    core_module,
    db_session: Session,
):
    response = test_client.post(
        url="/v1/tx"
        if endpoint_entity == "master_flows"
        else f"/v1/{endpoint_entity}/tx",
        json={"release_metadata": DUMMY_RELEASE_METADATA},
    )
    tx_uid = response.json()

    if endpoint_entity == "master_flows":
        first_payload = second_payload = get_master_flow_action_payload(tx_uid)
    elif endpoint_entity == "resources":
        first_payload = get_resource_action_payload(tx_uid, action="added")
        second_payload = get_resource_action_payload(tx_uid, action=endpoint_suffix)
    else:
        raise fail(f"Ошибка в тесте: не хватает ветки для сущности {endpoint_entity!r}")

    assert (
        test_client.post(
            url=f"/v1/{endpoint_entity}/actions/added", json=first_payload
        ).status_code
        == 200
    )

    response = test_client.post(
        url=f"/v1/{endpoint_entity}/actions/{endpoint_suffix}", json=second_payload
    )
    assert response.status_code == 409, response.text
    response_json = response.json()
    assert (
        response_json["msg"]
        == f"the transaction already has an action for this {http_409_msg_suffix}"
    )
    # Имена действий для разных сущностей совпадают, поэтому можно взять от любой
    assert response_json["existing_action"] == models.ResourceAction.ADD.name.lower()


@mark.parametrize(
    "endpoint_suffix",
    ["commit", "try"],
)
def test_rollebacks_transaction_on_etl_error(
    test_client,
    endpoint_suffix,
    master_flow,
    truncate_service,
    db_session: Session,
    monkeypatch,
):
    dummy_etl_errors = dict(dummy_flow=["dummy error message"])

    for func_name in (
        "run_release_sources_integration_etl",
        "dry_run_release_sources_integration_etl",
    ):
        monkeypatch.setattr(
            f"metaloader_rest_api.app.{func_name}",
            lambda *args, **kwargs: dummy_etl_errors,
        )

    response = test_client.post(
        "/v1/tx", json={"release_metadata": DUMMY_RELEASE_METADATA}
    )
    assert response.status_code == 200
    tx_uid = response.json()

    response = test_client.post(
        url=f"/v1/tx/{tx_uid}/{endpoint_suffix}",
        json={
            "mode": "incremental",
            "expected_master_flows": [],
        },
    )
    assert response.status_code == 409
    assert response.json()["master_flows"] == dummy_etl_errors

    response = test_client.get(f"/v1/tx/{tx_uid}")
    assert response.status_code == 200
    assert response.json()["status"] == schemas.TransactionStatus.ETL_ERROR.value


@mark.parametrize(
    "endpoint_suffix",
    ["commit", "try"],
)
def test_cant_commit_or_try_transaction_with_missing_master_flows(
    test_client,
    endpoint_suffix,
    db_session: Session,
    truncate_service,
    truncate_tables,
    core_module,
    master_flow,
    monkeypatch,
):
    response = test_client.post(
        "/v1/tx", json={"release_metadata": DUMMY_RELEASE_METADATA}
    )
    assert response.status_code == 200
    tx_uid = response.json()

    response = test_client.post(
        url="/v1/master_flows/actions/added",
        json={
            "tx_uid": tx_uid,
            "name": master_flow.master_flow_name,
            "definition": master_flow.model_dump(mode="json", exclude_unset=True),
        },
    )
    assert response.status_code == 200, response.text

    response = test_client.post(
        url=f"/v1/tx/{tx_uid}/{endpoint_suffix}",
        json={
            "mode": "incremental",
            "expected_master_flows": [master_flow.master_flow_name, "foo"],
        },
    )
    payload = response.json()
    assert response.status_code == 409
    assert payload["msg"] == "transaction is missing the expected master flows"
    assert payload["master_flows"] == {"foo": ["missing"]}

    assert (
        test_client.get(f"/v1/tx/{tx_uid}").json()["status"]
        == schemas.TransactionStatus.OPENED.value
    )


@mark.parametrize(
    "endpoint_suffix, expected_transaction_status",
    [
        ("commit", schemas.TransactionStatus.COMMITTED),
        ("try", schemas.TransactionStatus.ROLLEDBACK),
        ("rollback", schemas.TransactionStatus.ROLLEDBACK),
    ],
)
def test_transaction_resolution(
    test_client,
    endpoint_suffix,
    expected_transaction_status,
    db_session: Session,
    truncate_tables,
    truncate_service,
    app_url_factory,
    core_module,
    master_flow,
    monkeypatch,
):
    for func_name in (
        "run_release_sources_integration_etl",
        "dry_run_release_sources_integration_etl",
    ):
        monkeypatch.setattr(
            f"metaloader_rest_api.app.{func_name}",
            lambda *args, **kwargs: None,
        )

    response = test_client.post(
        "/v1/tx", json={"release_metadata": DUMMY_RELEASE_METADATA}
    )
    assert response.status_code == 200
    tx_uid = response.json()

    response = test_client.get(f"/v1/tx/{tx_uid}")
    assert response.status_code == 200
    transaction_info = response.json()
    assert transaction_info["status"] == schemas.TransactionStatus.OPENED.value

    response = test_client.post(
        url="/v1/master_flows/actions/added",
        json={
            "tx_uid": tx_uid,
            "name": master_flow.master_flow_name,
            "definition": master_flow.model_dump(mode="json", exclude_unset=True),
        },
    )
    assert response.status_code == 200

    response = test_client.post(
        url=f"/v1/tx/{tx_uid}/{endpoint_suffix}",
        json={
            "mode": "incremental",
            "expected_master_flows": [master_flow.master_flow_name],
        },
    )
    assert response.status_code == 200

    response = test_client.get(f"/v1/tx/{tx_uid}")
    transaction_info = response.json()
    assert transaction_info["status"] == expected_transaction_status.value


@mark.parametrize(
    "endpoint_prefix",
    [
        "/v1/tx",
        "/v1/resources/tx",
    ],
)
def test_rollback_timeout_transactions(
    test_client,
    endpoint_prefix,
):
    response = test_client.post(
        endpoint_prefix, json={"timeout": 0, "release_metadata": DUMMY_RELEASE_METADATA}
    )
    assert response.status_code == 200
    tx_uid = response.json()

    response = test_client.post(
        endpoint_prefix, json={"release_metadata": DUMMY_RELEASE_METADATA}
    )
    assert response.status_code == 200

    response = test_client.get(f"{endpoint_prefix}/{tx_uid}")
    assert response.status_code == 200
    transaction_info = response.json()
    assert transaction_info["status"] == schemas.TransactionStatus.ROLLEDBACK.value


def test_add(
    db_session: Session,
    truncate_tables,
    truncate_service,
    app_url_factory,
    core_module,
    master_flow,
):
    assert call_app(
        db_session=db_session,
        app_url_factory=app_url_factory,
        master_flow=master_flow,
    ) == (4, 3)


def test_add_invalid(
    db_session: Session,
    truncate_tables,
    truncate_service,
    app_url_factory,
    core_module,
    master_flow_invalid,
):
    assert (
        call_app(
            db_session=db_session,
            app_url_factory=app_url_factory,
            master_flow=master_flow_invalid,
        )
        == 406
    )


def test_delete(
    db_session: Session,
    truncate_tables,
    truncate_service,
    app_url_factory,
    core_module,
    master_flow,
):
    assert call_app(
        db_session=db_session,
        app_url_factory=app_url_factory,
        master_flow=master_flow,
    ) == (4, 3)

    assert call_app(
        db_session=db_session,
        app_url_factory=app_url_factory,
        master_flow_name=master_flow.master_flow_name,
        action="deleted",
        effective_date="2024-09-12",
    ) == (6, 6)


def test_rename(
    db_session: Session,
    truncate_tables,
    truncate_service,
    app_url_factory,
    core_module,
    master_flow,
):
    assert call_app(
        db_session=db_session,
        app_url_factory=app_url_factory,
        master_flow=master_flow,
    ) == (4, 3)

    assert call_app(
        db_session=db_session,
        app_url_factory=app_url_factory,
        master_flow_name=master_flow.master_flow_name,
        master_flow_name_new=f"{master_flow.master_flow_name}_new",
        action="renamed",
        effective_date="2024-09-12",
    ) == (6, 3)


def test_change(
    db_session: Session,
    truncate_tables,
    truncate_service,
    app_url_factory,
    core_module,
    master_flow,
    master_flow_changed,
):
    assert call_app(
        db_session=db_session,
        app_url_factory=app_url_factory,
        master_flow=master_flow,
    ) == (4, 3)

    assert call_app(
        db_session=db_session,
        app_url_factory=app_url_factory,
        master_flow=master_flow_changed,
        action="modified",
        effective_date="2024-09-12",
    ) == (6, 5)


def test_resource_actions_staging_happy_path(
    test_client,
    db_session: Session,
    ceh_resource_json,
    uni_resource_json,
    truncate_service,
    recreate_schema_stg,
):
    response = test_client.post(
        "/v1/resources/tx",
        json={"release_metadata": DUMMY_RELEASE_METADATA},
    )
    assert response.status_code == 200
    tx_id = response.json()

    assert (
        test_client.post(
            "/v1/resources/actions/added",
            json={
                "tx_uid": tx_id,
                "resource_cd": "a_non_matching_resource_cd",
                "resource_type": "ceh",
                "definition": ceh_resource_json,
            },
        ).status_code
        == 200
    )

    assert (
        test_client.post(
            "/v1/resources/actions/deleted",
            json={
                "tx_uid": tx_id,
                "resource_cd": "resource2",
            },
        ).status_code
        == 200
    )

    assert (
        test_client.post(
            "/v1/resources/actions/modified",
            json={
                "tx_uid": tx_id,
                "resource_cd": uni_resource_json["resource_cd"],
                "resource_type": "uni",
                "definition": uni_resource_json,
            },
        ).status_code
        == 200
    )

    result = db_session.execute(
        text(f"""
            SELECT resource_cd, data_action_type_rk, definition
            FROM stg.bridge_resource_{UUID(tx_id).hex}
            ORDER BY data_action_type_rk
        """)
    ).fetchall()

    assert result == [
        (ceh_resource_json["resource_cd"], 1, ceh_resource_json),
        (uni_resource_json["resource_cd"], 2, uni_resource_json),
        ("resource2", 4, None),
    ]

    response = test_client.get(f"/v1/resources/tx/{tx_id}")
    assert response.status_code == 200
    tx_info = response.json()
    assert tx_info["status"] == schemas.TransactionStatus.OPENED.value
    assert tx_info["release_metadata"] == DUMMY_RELEASE_METADATA
    assert set(tx_info["resources"]) == {
        ceh_resource_json["resource_cd"],
        uni_resource_json["resource_cd"],
        "resource2",
    }


def test_resource_transaction_happy_path(
    test_client,
    session_shortcuts_wrapper,
    uni_resource_json,
    ceh_resource_json,
    truncate_tables,
    truncate_service,
    recreate_schema_stg,
    cleanup_redis,
    core_module,
):
    #
    # Создаём таблицы и источники, с которыми будут связаны добавляемые ресурсы
    #
    # TODO: надо это отрефакторить -- сделать датаклассы с дефолтами для всех таблиц метамодели,
    #  чтобы не приходилось заполнять все поля, когда для теста нужны только несколько
    session_shortcuts_wrapper.execute(
        text(
            f"""
        INSERT INTO metamodel.bridge_table (
            table_rk,
            effective_from_dttm,
            effective_to_dttm,
            deleted_flg,
            version_rk,
            domain_rk,
            data_layer_rk,
            source_rk,
            schema_name,
            table_name,
            table_type_rk,
            table_distribution_type_rk,
            is_dict_flg,
            is_map_flg,
            is_hub_flg,
            is_sal_flg,
            is_mart_flg,
            is_bridge_flg,
            is_link_flg,
            is_temporal_flg,
            is_accessor_flg,
            version_scd_type_rk,
            history_scd_type_rk,
            is_deprecated_flg
        )
        VALUES
             (1001, '2025-01-01', '{LAST_DATE}', false, 1, 1, 1, 1, 'schema1', 'table1', 1, 1, false, false, false, false, false, false, false, false, false, 1, 1, false)
            ,(1002, '2025-01-01', '{LAST_DATE}', false, 1, 1, 1, 1, 'schema2', 'table2', 1, 1, false, false, false, false, false, false, false, false, false, 1, 1, false)
        ;

        INSERT INTO metamodel.bridge_source (
            source_rk,
            source_cd,
            ris_src_id,
            ris_src_code,
            source_desc,
            effective_from_dttm,
            effective_to_dttm,
            deleted_flg
        )
        VALUES
        (2001, 'source1', 'src_id_1', 'src_code_1', 'Test Source 1', '2025-01-01', '{LAST_DATE}', false)
        """
        )
    )
    session_shortcuts_wrapper.commit()

    #
    # Загружаем ресурсы
    #
    response = test_client.post(
        "/v1/resources/tx", json={"release_metadata": DUMMY_RELEASE_METADATA}
    )
    assert response.status_code == 200
    tx_id = response.json()

    uni_resource_json["resource_cd"] = "uni_resource_added"
    uni_resource_json["datasets"] = [{"name": "table1", "schema_name": "schema1"}]
    uni_resource_json["features"] = {"source_system": "source1"}

    response = test_client.post(
        "/v1/resources/actions/added",
        json={
            "tx_uid": tx_id,
            "resource_cd": "uni_resource_added",
            "resource_type": "uni",
            "definition": uni_resource_json,
        },
    )
    assert response.status_code == 200

    response = test_client.post(
        "/v1/resources/actions/deleted",
        json={"tx_uid": tx_id, "resource_cd": "uni_resource_deleted"},
    )
    assert response.status_code == 200

    ceh_resource_json["resource_cd"] = "ceh_resource_modified"
    ceh_resource_json["datasets"] = [{"name": "table2", "schema_name": "schema2"}]
    ceh_resource_json["features"] = {"source_system": "source1"}

    response = test_client.post(
        "/v1/resources/actions/modified",
        json={
            "tx_uid": tx_id,
            "resource_cd": "ceh_resource_modified",
            "resource_type": "ceh",
            "definition": ceh_resource_json,
        },
    )
    assert response.status_code == 200

    response = test_client.post(
        f"/v1/resources/tx/{tx_id}/commit", json={"mode": "incremental"}
    )
    assert response.status_code == 202, response.json()
    assert "Location" in response.headers

    # TODO: обощить `wait_for_committed_tx` на разные типы транзакций и использовать её здесь
    for _ in range(3):
        time.sleep(1)
        response = test_client.get(f"/v1/resources/tx/{tx_id}")
        assert response.status_code == 200
        if response.json()["status"] == "committed":
            break
    else:
        fail("Transaction not committed in time")

    #
    # Проверяем, что ресурсы и связи с источниками и таблицами загружены в метамодель
    #
    bridge_resource = session_shortcuts_wrapper.execute_fetchall(
        """
        SELECT
            resource_cd
          , effective_to_dttm
          , deleted_flg
        FROM metamodel.bridge_resource
        ORDER BY 1, 2
        """
    )
    assert bridge_resource == [
        ("ceh_resource_modified", LAST_DATE, False),
        ("uni_resource_added", LAST_DATE, False),
    ]

    link_resource_source = session_shortcuts_wrapper.execute_fetchall(
        """
        SELECT
            res.resource_cd
          , src.source_cd
          , link.effective_to_dttm
          , link.deleted_flg
        FROM
            metamodel.link_resource_source AS link
            INNER JOIN metamodel.bridge_resource AS res ON
                link.resource_rk = res.resource_rk
            INNER JOIN metamodel.bridge_source AS src ON
                link.source_rk = src.source_rk
        ORDER BY 1, 2, 3
        """
    )
    assert link_resource_source == [
        ("ceh_resource_modified", "source1", LAST_DATE, False),
        ("uni_resource_added", "source1", LAST_DATE, False),
    ]

    link_resource_table = session_shortcuts_wrapper.execute_fetchall(
        """
        SELECT
            res.resource_cd
          , tbl.table_name
          , tbl.schema_name
          , link.effective_to_dttm
          , link.deleted_flg
        FROM
            metamodel.link_resource_table AS link
            INNER JOIN metamodel.bridge_resource AS res ON
                link.resource_rk = res.resource_rk
            INNER JOIN metamodel.bridge_table AS tbl ON
                link.table_rk = tbl.table_rk
        ORDER BY 1, 2, 3
        """
    )
    assert link_resource_table == [
        ("ceh_resource_modified", "table2", "schema2", LAST_DATE, False),
        ("uni_resource_added", "table1", "schema1", LAST_DATE, False),
    ]


def test_transaction_info_endpoint_scoping(
    test_client,
    db_session: Session,
    truncate_service,
):
    response = test_client.post(
        "/v1/resources/tx",
        json={"release_metadata": DUMMY_RELEASE_METADATA},
    )
    assert response.status_code == 200
    resource_tx_id = response.json()

    response = test_client.get(f"/v1/tx/{resource_tx_id}")
    assert response.status_code == 404
    assert response.json()["detail"] == "Transaction not found"

    response = test_client.post(
        "/v1/tx",
        json={"release_metadata": DUMMY_RELEASE_METADATA},
    )
    assert response.status_code == 200
    masterflow_tx_id = response.json()

    response = test_client.get(f"/v1/resources/tx/{masterflow_tx_id}")
    assert response.status_code == 404
    assert response.json()["detail"] == "Transaction not found"

    response = test_client.get(f"/v1/resources/tx/{resource_tx_id}")
    assert response.status_code == 200

    response = test_client.get(f"/v1/tx/{masterflow_tx_id}")
    assert response.status_code == 200


def test_get_names(
    db_session: Session,
    truncate_tables,
    truncate_service,
    app_url_factory,
    core_module,
    master_flow,
    master_flow_changed,
):
    assert call_app(
        db_session=db_session,
        app_url_factory=app_url_factory,
        master_flow=master_flow,
    ) == (4, 3)

    mf_act = requests.get(
        url=app_url_factory("master_flows", "names", "core"),
        verify=False,
    )

    assert mf_act.ok
    assert mf_act.json() == [master_flow.master_flow_name]


def test_get_release(
    app_url_factory,
    truncate_tables,
    truncate_service,
    core_module,
):
    tx_info = requests.get(
        url=app_url_factory("releases", "core"),
        verify=False,
    )
    assert tx_info.status_code == 404

    tx_create = requests.post(
        url=app_url_factory("tx"),
        json={
            "release_metadata": {
                "release_module": core_module,
                "release_num": "1.2.3",
                "release_desc": "Release description",
                "release_date": "2024-09-06 00:00:00",
                "git_revision_hash": "1000000000000000000000000000000000000001",
                "effective_from_date": "2024-09-09 00:00:00",
            },
        },
        verify=False,
    )
    assert tx_create.ok
    tx_id = tx_create.json()

    tx_commit = requests.post(
        url=app_url_factory("tx", tx_id, "commit"),
        json={
            "mode": "incremental",
            "expected_master_flows": [],
        },
        verify=False,
    )
    assert tx_commit.ok

    tx_info = requests.get(
        url=app_url_factory("releases", "core"),
        verify=False,
    )
    assert tx_info.ok
    tx_git_revision = tx_info.json()["git_revision_hash"]
    assert tx_git_revision == "1000000000000000000000000000000000000001"


def test_load_flows_from_af(
    test_client,
    db_session,
    docker_af_url,
    celery_container,
    truncate_service,
    truncate_tables,
    core_module,
):
    response = test_client.post(
        url="/v1/flows/load_from_af",
        json={
            "module": "core",
            "release": "1.2.3",
            "af_url": docker_af_url,
            "page_size": 2,
            "limit": 3,
        },
    )
    assert response.status_code == 200

    tx_id = response.json()
    assert wait_for_committed_tx(test_client, tx_id) == (True, True)

    flows = db_session.execute(
        text("""
            SELECT *
              FROM metamodel.bridge_flow
         """)
    ).fetchall()

    assert len(flows) == 4


def test_load_flows_parameters(
    test_client,
    docker_file_server_url,
    db_session,
    truncate_tables,
    core_module,
    flow_paths,
    insert_flows_for_load_parameters,
    insert_versions_for_load_parameters,
):
    response = test_client.post(
        "/v1/flows/process",
        json={
            "deployment": {
                "module": "core",
                "release": "1.2.3",
            },
            "ceh_etl_src_url": f"{docker_file_server_url}/etl-scale/",
            "processors": ["idl_parameters"],
        },
    )
    assert response.status_code == 200

    assert wait_for_committed_tx(test_client, response.json()) == (True, True)

    parameters = db_session.execute(
        text("SELECT * FROM metamodel.bridge_flow_parameter")
    ).fetchall()

    assert len(parameters) == 1


def test_load_service(
    test_client,
    docker_file_server_url,
    db_session,
    truncate_service,
):
    response = test_client.post(
        url="/v1/service/load",
        json=[
            {
                "service_type_cd": "service_type_cd_1",
                "environment_cd": "environment_cd_1",
                "service_cd": "service_cd_1",
                "service_name": "service_name_1",
                "service_desc": "service_desc_1",
                "service_host_name": "service_host_name_1",
                "service_url": "service_url_1",
                "service_alt_url": "service_alt_url_1",
                "code_delivery_cd": "code_delivery_cd_1",
                "ris_src_id": "ris_src_id_1",
                "ris_src_code": "ris_src_code_1",
            }
        ],
    )
    assert response.status_code == 200

    assert wait_for_committed_tx(test_client, response.json()) == (True, True)

    services = db_session.execute(
        text("SELECT * FROM metamodel.bridge_service")
    ).fetchall()

    assert len(services) == 1


def wait_for_committed_tx(
    test_client,
    tx_uid,
    attempts: int = 10,
    timeout: int = 5,
) -> Tuple[bool, bool]:
    # TODO: непонятно, что означают эти "True, True". Стоит поменять тип результата на более
    #  читаемые значения: например, Literal[commit, error, timeout]
    for attempt in range(attempts):
        time.sleep(timeout)
        response = test_client.get(f"/v1/tx/{tx_uid}")

        if response.status_code != 200:
            return False, False

        if response.json()["status"] == schemas.TransactionStatus.COMMITTED.value:
            return True, True

    return True, False


def call_app(
    db_session: Session,
    app_url_factory: Callable[..., str],
    action: str = "added",
    effective_date: Optional[str] = "2024-09-09 00:00:00",
    master_flow: Optional[MasterFlow] = None,
    master_flow_name: Optional[str] = None,
    master_flow_name_new: Optional[str] = None,
) -> Union[Tuple[int, int], int]:
    if master_flow_name is None:
        master_flow_name = master_flow.master_flow_name

    tx_create = requests.post(
        url=app_url_factory("tx"),
        json={
            "release_metadata": {
                "release_module": "core",
                "release_num": "1.2.3",
                "release_desc": "Release description",
                "release_date": "2024-09-06 00:00:00",
                "git_revision_hash": "0000000000000000000000000000000000000000",
                "effective_from_date": effective_date,
            },
        },
        verify=False,
    )
    assert tx_create.ok
    tx_id = tx_create.json()

    mf_act = requests.post(
        url=app_url_factory("master_flows", "actions", action),
        json={
            "tx_uid": tx_id,
            "name": master_flow_name,
            "new_name": master_flow_name_new,
            "definition": master_flow.model_dump(mode="json", exclude_unset=True)
            if master_flow
            else None,
        },
        verify=False,
    )
    if not mf_act.ok:
        return mf_act.status_code

    tx_commit = requests.post(
        url=app_url_factory("tx", tx_id, "commit"),
        json={
            "mode": "incremental",
            "expected_master_flows": [master_flow_name_new or master_flow_name],
        },
        verify=False,
    )
    assert tx_commit.ok

    flows = db_session.scalar(
        text("""
        SELECT COUNT(*)
          FROM metamodel.bridge_flow
        """)
    )

    flow_links = db_session.scalar(
        text("""
        SELECT COUNT(*)
          FROM metamodel.link_flow
        """)
    )

    return flows, flow_links


@fixture(scope="function")
def flow_paths(
    db_session: Session,
) -> None:
    db_session.execute(text("TRUNCATE dict.dict_code_folder"))
    db_session.execute(
        statement=text("""
            INSERT INTO dict.dict_code_folder(code_folder_rk
                                            , code_folder_cd  
                                            , code_folder_path) 
                 VALUES (1
                       , 'rdv_control_flows'
                       , 'core/general_ledger/src_rdv/flow_dumps')
                      , (2
                       , 'idl_flows'
                       , 'core/general_ledger/rdv_idl/flow_dumps')
        """)
    )

    db_session.execute(text("TRUNCATE dict.link_code_delivery_folder"))
    db_session.execute(
        statement=text("""
            INSERT INTO dict.link_code_delivery_folder(code_delivery_rk
                                                     , code_folder_rk
                                                     , is_required_flg
                                                     , is_flow_flg
                                                     , is_master_flow_flg
                                                     , is_ddl_flg
                                                     , is_resource_flg)
                 VALUES (155
                       , 1
                       , TRUE
                       , TRUE
                       , FALSE
                       , FALSE
                       , FALSE)
                      , (155
                       , 2
                       , TRUE
                       , TRUE
                       , FALSE
                       , FALSE
                       , FALSE)
        """)
    )
    db_session.commit()


@fixture(scope="function")
def core_module(db_session) -> str:
    # TODO: прохождение тестов зависит от порядка применения фикстур: этой и truncate_flow.
    # Стоит сделать единую фикстуру, которая будет ресетитть состояние тестовой базы.
    db_session.execute(
        text("""
        INSERT INTO dict.dict_code_delivery(code_delivery_rk
                                          , code_delivery_cd
                                          , code_delivery_name
                                          , is_ddl_flg
                                          , is_flow_flg
                                          , is_config_flg
                                          , deleted_flg)
        VALUES (155
              , 'core'
              , 'core'
              , FALSE
              , TRUE
              , FALSE
              , FALSE)
    """)
    )
    db_session.commit()

    return "core"


@fixture
def get_resource_action_payload(ceh_resource_json, uni_resource_json):
    def get_payload(
        tx_uid: str,
        action: Literal["added", "modified", "deleted"],
        resource_type: Literal["ceh", "uni"] = "ceh",
    ) -> dict:
        payload = dict(
            tx_uid=tx_uid,
            resource_cd=ceh_resource_json["resource_cd"],
            resource_type=schemas.ResourceType.CEH.value
            if resource_type == "ceh"
            else schemas.ResourceType.UNI.value,
            definition=ceh_resource_json
            if resource_type == "ceh"
            else uni_resource_json,
        )
        if action == "deleted":
            del payload["resource_type"]
            del payload["definition"]
        return payload

    return get_payload


@fixture(scope="module")
def get_master_flow_action_payload(master_flow):
    def get_payload(tx_uid: str):
        return dict(
            tx_uid=tx_uid,
            name="foo",
            new_name="bar",
            definition=master_flow.model_dump(mode="json", exclude_unset=True),
        )

    return get_payload


@fixture(scope="module")
def master_flow() -> MasterFlow:
    return create_master_flow(steps=["test"])


@fixture(scope="module")
def master_flow_changed() -> MasterFlow:
    return create_master_flow(steps=["test", "test_another"])


@fixture(scope="module")
def master_flow_invalid() -> MasterFlow:
    return MasterFlow(
        master_flow_name="cf_master_test",
        groups=[
            FlowGroup(
                group_id="group_test",
                steps=[
                    FlowStep(
                        cf_flow_id="cf_test",
                        wrk_flow_id="wrk_test",
                        dependencies=["cf_test_another"],
                    )
                ],
            ),
            FlowGroup(
                group_id="group_test_another",
                steps=[],
            ),
            FlowGroup(
                group_id="group_test_another",
                steps=[],
            ),
        ],
    )


def create_master_flow(steps: List[str]) -> MasterFlow:
    return MasterFlow(
        master_flow_name="cf_master_test",
        groups=[
            FlowGroup(
                group_id="group_test",
                steps=[
                    FlowStep(
                        cf_flow_id=f"cf_{step}",
                        wrk_flow_id=f"wrk_{step}",
                    )
                    for step in steps
                ],
            ),
        ],
    )


@fixture(scope="function")
def truncate_tables(db_session: Session):
    db_session.execute(text("TRUNCATE dict.dict_code_delivery"))
    db_session.execute(text("TRUNCATE metamodel.bridge_version"))
    db_session.execute(text("TRUNCATE metamodel.bridge_vcs_repository_version"))
    db_session.execute(text("TRUNCATE metamodel.bridge_flow"))
    db_session.execute(text("TRUNCATE metamodel.link_flow"))
    db_session.execute(text("TRUNCATE metamodel.bridge_flow_parameter"))
    db_session.execute(text("TRUNCATE metamodel.link_flow_parameter"))
    db_session.execute(text("TRUNCATE metamodel.bridge_table"))
    db_session.execute(text("TRUNCATE metamodel.bridge_source"))
    db_session.execute(text("TRUNCATE metamodel.bridge_resource"))
    db_session.execute(text("TRUNCATE metamodel.link_resource_source"))
    db_session.execute(text("TRUNCATE metamodel.link_resource_table"))
    db_session.commit()


# noinspection SqlWithoutWhere
@fixture(scope="function")
def truncate_service(db_session: Session):
    db_session.execute(
        text(
            "TRUNCATE metamodel.service_transaction_data, metamodel.service_transaction_status"
        )
    )
    db_session.commit()


@fixture(scope="function")
def cleanup_redis(redis_client):
    redis_client.flushdb()


@fixture(scope="function")
def insert_flows_for_load_parameters(db_session: Session):
    with SessionResource(db_session):
        db_session.execute(text("TRUNCATE metamodel.bridge_flow"))
        db_session.execute(
            text("""
            INSERT INTO metamodel.bridge_flow(flow_rk, flow_name, flow_type_rk, deleted_flg, code_delivery_rk, version_rk, effective_from_dttm, effective_to_dttm, flow_release_status_rk)
                 VALUES (1, 'flow_test_1', 2, FALSE, 155, 1, '2024-11-19 20:53:50.656849 +00:00', '2999-12-31 00:00:00.000000 +00:00', 1)
        """)
        )
        db_session.commit()


@fixture(scope="function")
def insert_versions_for_load_parameters(db_session: Session):
    with SessionResource(db_session):
        db_session.execute(text("TRUNCATE metamodel.bridge_version"))
        db_session.execute(
            text("""
            INSERT INTO metamodel.bridge_version (version_rk, code_delivery_rk, release_major_num, release_minor_num, release_fix_num, release_delivery_dttm)
                 VALUES (1, 155, 1, 2, 3, '2025-01-24 00:10:22.037791 +00:00')
        """)
        )
        db_session.commit()


@fixture(scope="function")
def recreate_schema_stg(db_session: Session):
    db_session.execute(text("DROP SCHEMA IF EXISTS stg CASCADE"))
    db_session.execute(text("CREATE SCHEMA stg"))
    db_session.commit()


@fixture(scope="function")
def truncate_service(db_session):
    db_session.execute(text("TRUNCATE TABLE metamodel.bridge_service"))
    db_session.commit()
