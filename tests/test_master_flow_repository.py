from datetime import datetime

from metaloader_rest_api.common_repository import SessionResource
from metaloader_rest_api.flow_model import FlowType
from metaloader_rest_api.flow_repository import (
    FlowRepository,
    StageFlow,
    StageFlowRepository,
    StageMasterFlowLinkRepository,
    StageMasterFlowRepository,
)
from metaloader_rest_api.master_files_schemas import (
    ControlFlow as MasterFlow,
)
from metaloader_rest_api.master_files_schemas import (
    FlowGroup,
)
from metaloader_rest_api.master_files_schemas import (
    TriggerFlow as FlowStep,
)
from metaloader_rest_api.master_flow_repository import (
    MasterFlowRepository,
    MasterFlowRepositoryException,
    load_master_flows,
)
from metaloader_rest_api.models import (
    Master<PERSON>lowAction,
    MasterFlowTransaction,
    MasterFlowTransactionItem,
    TransactionStatus,
)
from metaloader_rest_api.schemas import ReleaseMetadata
from metaloader_rest_api.vcs_repository_repository import VcsRepositoryRepository
from metaloader_rest_api.vcs_repository_version_repository import (
    VcsRepositoryVersionRepository,
)
from metaloader_rest_api.version_repository import VersionRepository
from pytest import fixture, mark, raises
from sqlalchemy import text
from sqlalchemy.orm import Session


def test_add_flow(
    db_session: Session,
    truncate_flow,
    session_resource: SessionResource,
    table_id: str,
    module_id,
    version_id,
):
    with session_resource, StageFlowRepository(
        db_session, table_id
    ) as stage_flow_repository, StageMasterFlowRepository(
        db_session, table_id
    ) as stage_master_flow_repository, StageMasterFlowLinkRepository(
        db_session, table_id
    ) as stage_master_flow_link_repository:
        stage_flow_repository.load(
            [
                StageFlow(
                    name="cf_test_1",
                    type=FlowType.CONTROL.value,
                    description=None,
                    tags=None,
                    dag=None,
                    tasks=None,
                ),
                StageFlow(
                    name="wrk_test_1",
                    type=FlowType.WORK.value,
                    description=None,
                    tags=None,
                    dag=None,
                    tasks=None,
                ),
            ]
        )
    with session_resource:
        flow_repository = FlowRepository(db_session)
        flow_repository.load(
            module_id,
            version_id,
            effective_date=datetime(year=2024, month=6, day=15),
            flow_stage_table=stage_flow_repository.table,
            master_flow_stage_table=stage_master_flow_repository.table,
            master_flow_link_stage_table=stage_master_flow_link_repository.table,
        )

    master_flow = MasterFlow(
        master_flow_name="cf_master_test",
        groups=[
            FlowGroup(
                group_id="test_group_1",
                steps=[
                    FlowStep(
                        cf_flow_id="cf_test_1",
                        wrk_flow_id="wrk_test_1",
                    ),
                ],
            ),
            FlowGroup(
                group_id="test_group_2",
                steps=[
                    FlowStep(
                        cf_flow_id="cf_test_2_1",
                        wrk_flow_id="wrk_test_2_1",
                    ),
                    FlowStep(
                        cf_flow_id="cf_test_2_2",
                        wrk_flow_id="wrk_test_2_2",
                        dependencies=["cf_test_2_1"],
                    ),
                    FlowStep(
                        cf_flow_id="wrk_test_3",
                        wrk_flow_id="wrk_test_3",
                    ),
                ],
            ),
        ],
    )
    repository = MasterFlowRepository(session=db_session)
    with session_resource:
        repository.set_module_id(module_id)
        repository.set_version_id(version_id)
        repository.set_effective_date(datetime(year=2024, month=6, day=21))
        repository.add_flow(name="cf_master_test", flow=master_flow)

    flows = db_session.execute(
        text("""
      SELECT flow_rk
           , flow_type_rk
           , flow_release_status_rk
           , flow_name
           , flow_desc
           , version_rk
           , effective_from_dttm
           , effective_to_dttm
           , deleted_flg
        FROM metamodel.bridge_flow
    ORDER BY flow_rk
    """)
    ).fetchall()
    assert len(flows) == 10

    flow_links = db_session.execute(
        text("""
          SELECT owner_flow_rk
               , source_flow_rk
               , target_flow_rk
               , flow_link_type_rk
               , trigger_timeout_duration
               , retry_timeout_duration
               , delta_timeout_duration
               , flow_timeout_duration
               , trigger_cnt
               , retry_cnt
               , is_active_flg
               , is_required_flg
               , is_delta_required_flg
               , is_status_check_flg
               , is_resource_status_check_flg
               , version_rk
               , effective_from_dttm
               , effective_to_dttm
               , deleted_flg
            FROM metamodel.link_flow
        ORDER BY owner_flow_rk
               , source_flow_rk
               , target_flow_rk
               , flow_link_type_rk
        """)
    ).fetchall()
    assert len(flow_links) == 13


@mark.skip
def test_add_existing_flow(
    db_session: Session,
    truncate_flow,
    session_resource: SessionResource,
    module_id,
    version_id,
):
    repository = MasterFlowRepository(session=db_session)
    repository.set_module_id(module_id)
    repository.set_version_id(version_id)

    master_flow = MasterFlow(
        master_flow_name="cf_master_test",
        groups=[
            FlowGroup(
                group_id="test_group",
                steps=[
                    FlowStep(
                        cf_flow_id="cf_test",
                        wrk_flow_id="wrk_test",
                    ),
                ],
            ),
        ],
    )

    with session_resource:
        repository.add_flow(name="cf_master_test", flow=master_flow)

    with raises(
        MasterFlowRepositoryException, match="Flow cf_master_test already exists"
    ):
        with session_resource:
            repository.add_flow(name="cf_master_test", flow=master_flow)


def test_delete_flow(
    db_session: Session,
    truncate_flow,
    session_resource: SessionResource,
    module_id,
    version_id,
):
    repository = MasterFlowRepository(session=db_session)
    repository.set_module_id(module_id)
    repository.set_version_id(version_id)

    master_flow = MasterFlow(
        master_flow_name="cf_master_test",
        groups=[
            FlowGroup(
                group_id="test_group_1",
                steps=[
                    FlowStep(
                        cf_flow_id="cf_test_1",
                        wrk_flow_id="wrk_test_1",
                    ),
                ],
            ),
            FlowGroup(
                group_id="test_group_2",
                steps=[
                    FlowStep(
                        cf_flow_id="cf_test_2_1",
                        wrk_flow_id="wrk_test_2_1",
                    ),
                    FlowStep(
                        cf_flow_id="cf_test_2_2",
                        wrk_flow_id="wrk_test_2_2",
                        dependencies=["cf_test_2_1"],
                    ),
                ],
            ),
        ],
    )
    with session_resource:
        repository.add_flow(name="cf_master_test", flow=master_flow)

    repository.set_effective_date(datetime(2024, 9, 9))
    with session_resource:
        repository.delete_flow(name="cf_master_test")

    flows = db_session.scalar(
        text("""
      SELECT COUNT(*)
        FROM metamodel.bridge_flow
       WHERE deleted_flg 
    """)
    )
    assert flows == 3

    flow_links = db_session.scalar(
        text("""
          SELECT COUNT(*)
            FROM metamodel.link_flow
           WHERE deleted_flg 
        """)
    )
    assert flow_links == 9


@mark.skip
def test_delete_non_existing_flow(
    db_session: Session,
    truncate_flow,
    module_id,
    version_id,
    session_resource: SessionResource,
):
    with raises(
        MasterFlowRepositoryException, match="Flow cf_master_test doesn't exist"
    ):
        with session_resource:
            repository = MasterFlowRepository(session=db_session)
            repository.set_module_id(module_id)
            repository.set_version_id(version_id)
            repository.delete_flow(name="cf_master_test")


def test_rename_flow(
    db_session: Session,
    truncate_flow,
    module_id,
    version_id,
    session_resource: SessionResource,
):
    repository = MasterFlowRepository(session=db_session)
    repository.set_module_id(module_id)
    repository.set_version_id(version_id)

    master_flow = MasterFlow(
        master_flow_name="cf_master_test",
        groups=[
            FlowGroup(
                group_id="test_group_1",
                steps=[
                    FlowStep(
                        cf_flow_id="cf_test_1",
                        wrk_flow_id="wrk_test_1",
                    ),
                ],
            ),
        ],
    )
    with session_resource:
        repository.add_flow(name="cf_master_test", flow=master_flow)

    repository.set_effective_date(datetime(2024, 9, 9))
    with session_resource:
        repository.rename_flow(name="cf_master_test", new_name="cf_master_test_new")

    flows = db_session.scalar(
        statement=text("""
        SELECT COUNT(*)
          FROM metamodel.bridge_flow
         WHERE flow_name LIKE :flow_name || '%'
        """),
        params={
            "flow_name": "cf_master_test_new",
        },
    )
    assert flows == 2

    flow_links = db_session.scalar(
        text("""
          SELECT COUNT(*)
            FROM metamodel.link_flow
        """)
    )
    assert flow_links == 3


def test_rename_non_existing_flow(
    db_session: Session, truncate_flow, session_resource: SessionResource, module_id
):
    with raises(
        MasterFlowRepositoryException, match="Flow cf_master_test doesn't exist"
    ):
        with session_resource:
            repository = MasterFlowRepository(session=db_session)
            repository.set_module_id(module_id)
            repository.set_version_id(1)
            repository.rename_flow(name="cf_master_test", new_name="cf_master_test_new")


def test_change_flow(
    db_session: Session,
    truncate_flow,
    module_id,
    version_id,
    session_resource: SessionResource,
):
    repository = MasterFlowRepository(session=db_session)
    repository.set_module_id(module_id)
    repository.set_version_id(version_id)

    master_flow = MasterFlow(
        master_flow_name="cf_master_test",
        groups=[
            FlowGroup(
                group_id="test_group_1",
                steps=[
                    FlowStep(
                        cf_flow_id="cf_test_1_1",
                        wrk_flow_id="wrk_test_1_1",
                    ),
                ],
            ),
            FlowGroup(
                group_id="test_group_2",
                steps=[
                    FlowStep(
                        cf_flow_id="cf_test_2_1",
                        wrk_flow_id="wrk_test_2_1",
                    ),
                    FlowStep(
                        cf_flow_id="cf_test_2_2",
                        wrk_flow_id="wrk_test_2_2",
                    ),
                    FlowStep(
                        cf_flow_id="cf_test_2_4",
                        wrk_flow_id="wrk_test_2_4",
                        dependencies=["cf_test_2_1", "cf_test_2_2"],
                    ),
                    FlowStep(
                        cf_flow_id="cf_test_2_5",
                        wrk_flow_id="wrk_test_2_5",
                        dependencies=["cf_test_2_4"],
                    ),
                ],
            ),
        ],
    )
    with session_resource:
        repository.add_flow(name="cf_master_test", flow=master_flow)

    master_flow = MasterFlow(
        master_flow_name="cf_master_test",
        groups=[
            FlowGroup(
                group_id="test_group_1",
                steps=[
                    FlowStep(
                        cf_flow_id="cf_test_1_1",
                        wrk_flow_id="wrk_test_1_1",
                    ),
                    FlowStep(
                        cf_flow_id="cf_test_1_2",
                        wrk_flow_id="wrk_test_1_2",
                    ),
                ],
            ),
            FlowGroup(
                group_id="test_group_2",
                steps=[
                    FlowStep(
                        cf_flow_id="cf_test_2_1",
                        wrk_flow_id="wrk_test_2_1",
                    ),
                    FlowStep(
                        cf_flow_id="cf_test_2_3",
                        wrk_flow_id="wrk_test_2_3",
                    ),
                    FlowStep(
                        cf_flow_id="cf_test_2_4",
                        wrk_flow_id="wrk_test_2_4",
                        dependencies=["cf_test_2_1", "cf_test_2_3"],
                    ),
                    FlowStep(
                        cf_flow_id="cf_test_2_5",
                        wrk_flow_id="wf_test_2_5",
                        dependencies=["cf_test_2_4"],
                    ),
                ],
            ),
            FlowGroup(
                group_id="test_group_3",
                steps=[
                    FlowStep(
                        cf_flow_id="cf_test_3_1",
                        wrk_flow_id="wrk_test_3_1",
                    ),
                ],
            ),
        ],
    )
    repository.set_effective_date(datetime(2024, 9, 9))
    with session_resource:
        repository.change_flow(name="cf_master_test", flow=master_flow)

    flows = db_session.execute(
        text("""
          SELECT flow_rk
               , flow_type_rk
               , flow_release_status_rk
               , flow_name
               , flow_desc
               , version_rk
               , effective_from_dttm
               , effective_to_dttm
               , deleted_flg
            FROM metamodel.bridge_flow
        ORDER BY flow_rk
        """)
    ).fetchall()
    assert len(flows) == 21

    flow_links = db_session.execute(
        text("""
          SELECT owner_flow_rk
               , source_flow_rk
               , target_flow_rk
               , flow_link_type_rk
               , trigger_timeout_duration
               , retry_timeout_duration
               , delta_timeout_duration
               , flow_timeout_duration
               , trigger_cnt
               , retry_cnt
               , is_active_flg
               , is_required_flg
               , is_delta_required_flg
               , is_status_check_flg
               , is_resource_status_check_flg
               , version_rk
               , effective_from_dttm
               , effective_to_dttm
               , deleted_flg
            FROM metamodel.link_flow
        ORDER BY owner_flow_rk
               , source_flow_rk
               , target_flow_rk
               , flow_link_type_rk
        """)
    ).fetchall()
    assert len(flow_links) == 28


@mark.skip
def test_change_non_existing_flow(
    db_session: Session,
    truncate_flow,
    module_id,
    version_id,
    session_resource: SessionResource,
):
    master_flow = MasterFlow(
        master_flow_name="cf_master_test",
        groups=[
            FlowGroup(
                group_id="test_group",
                steps=[
                    FlowStep(
                        cf_flow_id="cf_test",
                        wrk_flow_id="wrk_test",
                    ),
                ],
            ),
        ],
    )

    with session_resource:
        repository = MasterFlowRepository(session=db_session)
        repository.set_module_id(module_id)
        repository.set_version_id(version_id)
        repository.change_flow(name="cf_master_test", flow=master_flow)

    flows = db_session.execute(
        text("""
      SELECT flow_rk
           , flow_type_rk
           , flow_release_status_rk
           , flow_name
           , flow_desc
           , version_rk
           , effective_from_dttm
           , effective_to_dttm
           , deleted_flg
        FROM metamodel.bridge_flow
    ORDER BY flow_rk
    """)
    ).fetchall()
    assert len(flows) == 4

    flow_links = db_session.execute(
        text("""
          SELECT owner_flow_rk
               , source_flow_rk
               , target_flow_rk
               , flow_link_type_rk
               , trigger_timeout_duration
               , retry_timeout_duration
               , delta_timeout_duration
               , flow_timeout_duration
               , trigger_cnt
               , retry_cnt
               , is_active_flg
               , is_required_flg
               , is_delta_required_flg
               , is_status_check_flg
               , is_resource_status_check_flg
               , version_rk
               , effective_from_dttm
               , effective_to_dttm
               , deleted_flg
            FROM metamodel.link_flow
        ORDER BY owner_flow_rk
               , source_flow_rk
               , target_flow_rk
               , flow_link_type_rk
        """)
    ).fetchall()
    assert len(flow_links) == 3


def test_get_flow_names(
    db_session: Session,
    truncate_flow,
    session_resource: SessionResource,
    module_id,
    version_id,
    revision_id,
):
    vcs_repository_repository = VcsRepositoryRepository(db_session)
    vcs_repository_id = vcs_repository_repository.get_id("DTPL/ceh-config")

    vcs_repository_version_repository = VcsRepositoryVersionRepository(db_session)

    effective_date = datetime(2024, 6, 15)

    repository = MasterFlowRepository(session=db_session)
    repository.set_module_id(module_id)
    repository.set_version_id(version_id)
    repository.set_effective_date(effective_date)

    assert repository.get_flow_names() == []

    master_flow = MasterFlow(
        master_flow_name="cf_master_test_1",
        groups=[
            FlowGroup(
                group_id="test_group_1",
                steps=[
                    FlowStep(
                        cf_flow_id="cf_test_1",
                        wrk_flow_id="wrk_test_1",
                    ),
                ],
            ),
        ],
    )

    with session_resource:
        vcs_repository_version_repository.put(
            vcs_repository_id, module_id, revision_id, version_id, effective_date
        )
        repository.add_flow(name="cf_master_test_1", flow=master_flow)

    assert repository.get_flow_names() == ["cf_master_test_1"]

    master_flow = MasterFlow(
        master_flow_name="cf_master_test_2",
        groups=[
            FlowGroup(
                group_id="test_group_2",
                steps=[
                    FlowStep(
                        cf_flow_id="cf_test_2",
                        wrk_flow_id="wrk_test_2",
                    ),
                ],
            ),
        ],
    )

    with session_resource:
        repository.add_flow(name="cf_master_test_2", flow=master_flow)

    assert repository.get_flow_names() == [
        "cf_master_test_1",
        "cf_master_test_2",
    ]

    repository.set_effective_date(datetime.now())
    with session_resource:
        repository.delete_flow(name="cf_master_test_1")

    assert repository.get_flow_names() == ["cf_master_test_2"]


def test_load_flows(
    db_session: Session,
    session_resource: SessionResource,
    truncate_flow,
    truncate_service,
    module_id,
    version_id,
    revision_id,
):
    release_metadata = ReleaseMetadata(
        release_module="core",
        release_num="1.2.3",
        release_desc="Release description",
        release_date=datetime(2024, 9, 1),
        git_revision_hash=revision_id,
        effective_from_date=datetime(2024, 9, 6),
    )

    transaction = MasterFlowTransaction(
        status=TransactionStatus.OPENED.value,
        release_metadata=release_metadata.model_dump(mode="json"),
    )
    db_session.add(transaction)
    db_session.flush()

    mf = MasterFlow(
        master_flow_name="cf_master_test",
        groups=[
            FlowGroup(
                group_id="test_group",
                steps=[
                    FlowStep(
                        cf_flow_id="cf_test",
                        wrk_flow_id="wrk_test",
                    ),
                ],
            ),
        ],
    )

    transaction_mf = MasterFlowTransactionItem(
        name="cf_master_test",
        content_json=mf.model_dump(mode="json"),
        action=MasterFlowAction.ADD.value,
        transaction_id=transaction.id,
    )
    db_session.add(transaction_mf)
    db_session.flush()

    db_session.commit()

    load_master_flows(session=db_session, tx_id=transaction.id)


@fixture(scope="function")
def module_id(db_session) -> int:
    db_session.execute(
        text("""
        INSERT INTO dict.dict_code_delivery(code_delivery_rk
                                          , code_delivery_cd
                                          , code_delivery_name
                                          , is_ddl_flg
                                          , is_flow_flg
                                          , is_config_flg
                                          , deleted_flg)
        VALUES (1
              , 'core'
              , 'core'
              , FALSE
              , TRUE
              , FALSE
              , FALSE)
    """)
    )
    db_session.commit()

    return 1


@fixture(scope="function")
def version_id(
    db_session,
    session_resource,
    module_id,
) -> int:
    with session_resource:
        version_repository = VersionRepository(db_session)
        return version_repository.put(
            module_id,
            major=1,
            minor=2,
            fix=3,
        )


@fixture(scope="module")
def table_id() -> str:
    return 32 * "0"


@fixture(scope="module")
def revision_id() -> str:
    return 40 * "0"


@fixture(scope="function")
def truncate_flow(db_session: Session):
    db_session.execute(text("TRUNCATE dict.dict_code_delivery"))
    db_session.execute(text("TRUNCATE metamodel.bridge_version"))
    db_session.execute(text("TRUNCATE metamodel.bridge_vcs_repository_version"))
    db_session.execute(text("TRUNCATE metamodel.bridge_flow"))
    db_session.execute(text("TRUNCATE metamodel.link_flow"))
    db_session.commit()


@fixture(scope="function")
def truncate_service(db_session: Session):
    db_session.execute(text("DELETE FROM metamodel.service_transaction_data"))
    db_session.execute(text("DELETE FROM metamodel.service_transaction_status"))
    db_session.commit()
