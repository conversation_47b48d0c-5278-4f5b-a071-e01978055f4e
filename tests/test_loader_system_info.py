import uuid
from io import Bytes<PERSON>
from operator import itemgetter

import pandas as pd
import pytest
from data.test_data_for_loader_system_info import (
    EXPECTED_INC_1_BRIDGE_SOURCE_DATA,
    EXPECTED_INC_2_BRIDGE_SOURCE_DATA,
    EXPECTED_INIT_BRIDGE_SOURCE_DATA,
    INC_1_BRIDGE_SOURCE_DATA,
    INC_2_BRIDGE_SOURCE_DATA,
    INIT_BRIDGE_SOURCE_DATA,
    LOAD_ID_INC_1,
    LOAD_ID_INC_2,
    LOAD_ID_INIT,
    NOW_INC_1,
    NOW_INC_2,
    NOW_INIT,
)
from fastapi.testclient import TestClient
from metaloader_rest_api.app import app
from metaloader_rest_api.loader_info_system.services.handler import process_xlsx_files
from pytest import fixture
from sqlalchemy import text
from sqlalchemy.orm import Session


@fixture(scope="function")
def client():
    return TestClient(app)


@fixture(scope="function")
def truncate_bridge_source(db_session: Session) -> None:
    db_session.execute(text("TRUNCATE metamodel.bridge_source"))
    db_session.commit()


def generate_test_excel(data: list[dict], sheet_name: str) -> BytesIO:
    df = pd.DataFrame(data)

    buffer = BytesIO()
    with pd.ExcelWriter(buffer, engine="openpyxl") as writer:
        df.to_excel(writer, index=False, sheet_name=sheet_name)
    buffer.seek(0)

    return buffer


def validate_stg_bridge_source(
    db_session: Session, tx_uid: uuid.UUID, expected_data: list[dict]
) -> None:
    tx_hex = tx_uid.hex
    staging_table = f"stg.stg_bridge_source_{tx_hex}"
    query = f"""
        SELECT
            source_cd,
            parent_source_cd,
            short_cd,
            ris_src_id,
            ris_src_code,
            source_desc,
            data_layer_rk
        FROM {staging_table}
    """
    result_staging = db_session.execute(text(query)).mappings().all()

    expected_sorted = sorted(expected_data, key=itemgetter("source_cd"))
    actual_sorted = sorted(result_staging, key=itemgetter("source_cd"))

    assert expected_sorted == actual_sorted


def validate_meta_bridge_source(db_session: Session, expected_data: list[dict]) -> None:
    query = """
        SELECT
            source_cd,
            short_cd,
            ris_src_id,
            ris_src_code,
            source_desc,
            data_layer_rk,
            effective_from_dttm,
            effective_to_dttm,
            deleted_flg
        FROM metamodel.bridge_source
    """
    result_target = db_session.execute(text(query)).mappings().all()

    expected_sorted = sorted(
        expected_data, key=itemgetter("source_cd", "effective_from_dttm")
    )
    actual_sorted = sorted(
        result_target, key=itemgetter("source_cd", "effective_from_dttm")
    )

    assert actual_sorted == expected_sorted


@pytest.mark.dependency()
def test_init_load_system_info(
    client: TestClient, db_session: Session, truncate_bridge_source: None
) -> None:
    test_excel = generate_test_excel(INIT_BRIDGE_SOURCE_DATA, "bridge_source")
    response = client.post(
        "/v1/sources/load_from_xlsx",
        files={
            "xlsx_file": (
                "test_stg_bridge_source.xlsx",
                test_excel,
                "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            )
        },
        data={"effective_from_dttm": NOW_INIT.isoformat()},
    )
    assert response.status_code == 200

    tx_uid = uuid.UUID(response.text.strip('"'))

    validate_stg_bridge_source(db_session, tx_uid, INIT_BRIDGE_SOURCE_DATA)
    validate_meta_bridge_source(db_session, EXPECTED_INIT_BRIDGE_SOURCE_DATA)


@pytest.mark.dependency(depends=["test_init_load_system_info"])
def test_repeat_init_load_system_info(db_session: Session) -> None:
    excel_init = generate_test_excel(INIT_BRIDGE_SOURCE_DATA, "bridge_source").read()
    process_xlsx_files(
        session=db_session,
        load_id=LOAD_ID_INIT,
        file_name="stg_bridge_source",
        loaded_file=excel_init,
        effective_from_dttm=NOW_INIT.isoformat(),
    )
    validate_stg_bridge_source(db_session, LOAD_ID_INIT, INIT_BRIDGE_SOURCE_DATA)
    validate_meta_bridge_source(db_session, EXPECTED_INIT_BRIDGE_SOURCE_DATA)


@pytest.mark.skip("Выключил на время разработки МЕТАМ-482, пока тест сломан")
@pytest.mark.dependency(depends=["test_init_load_system_info"])
def test_increment_load_system_info(db_session: Session) -> None:
    excel_inc_1 = generate_test_excel(INC_1_BRIDGE_SOURCE_DATA, "bridge_source").read()
    result_inc_1 = process_xlsx_files(
        session=db_session,
        load_id=LOAD_ID_INC_1,
        file_name="stg_bridge_source",
        loaded_file=excel_inc_1,
        effective_from_dttm=NOW_INC_1.isoformat(),
    )
    validate_stg_bridge_source(db_session, LOAD_ID_INC_1, INC_1_BRIDGE_SOURCE_DATA)
    validate_meta_bridge_source(db_session, EXPECTED_INC_1_BRIDGE_SOURCE_DATA)
    assert (
        str(result_inc_1)
        == f"stg_bridge_source_{LOAD_ID_INC_1.hex} and bridge_source loaded"
    )

    excel_inc_2 = generate_test_excel(INC_2_BRIDGE_SOURCE_DATA, "bridge_source").read()
    result_inc_2 = process_xlsx_files(
        session=db_session,
        load_id=LOAD_ID_INC_2,
        file_name="stg_bridge_source",
        loaded_file=excel_inc_2,
        effective_from_dttm=NOW_INC_2.isoformat(),
    )
    validate_stg_bridge_source(db_session, LOAD_ID_INC_2, INC_2_BRIDGE_SOURCE_DATA)
    validate_meta_bridge_source(db_session, EXPECTED_INC_2_BRIDGE_SOURCE_DATA)
    assert (
        str(result_inc_2)
        == f"stg_bridge_source_{LOAD_ID_INC_2.hex} and bridge_source loaded"
    )
